# Hierarchical Mixed-Effects Model Analysis for ABI Promotion Data

This notebook analyzes the effect of various KPIs on ABI MS Uplift (relative) using hierarchical/mixed-effects modeling to account for retailer, brand, and pack variability.

**Target KPIs to analyze:**
- ABI_Duration_Days
- ABI Mechanic
- Overlapping Days
- Same Week, Before (1-2 wk), After (1-2 wk)
- Avg Temp
- ABI vs Segment PTC Index Agg
- ABI_Coverage
- KSM (ADDED)
Split Before and After into 1-2 wk before and 1-2 wk after

**Target Variable:** ABI MS Uplift rel


# Imports and setup
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
import statsmodels.api as sm
import statsmodels.formula.api as smf
from statsmodels.regression.mixed_linear_model import MixedLM
from scipy import stats
from sklearn.preprocessing import StandardScaler
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Libraries loaded successfully")


## Data Loading and Initial Exploration


def load_and_explore_data(data_path):
    """Load data and perform initial exploration"""
    logger.info("Loading and exploring data...")

    # Load data
    df_raw = pd.read_csv(data_path)
    logger.info(f"Loaded data with shape: {df_raw.shape}")

    # Basic info
    print("="*80)
    print("DATA OVERVIEW")
    print("="*80)
    print(f"Dataset shape: {df_raw.shape}")
    print(f"Columns: {list(df_raw.columns)}")
    print("\nFirst few rows:")
    print(df_raw.head())

    print("\nData types:")
    print(df_raw.dtypes)

    print("\nMissing values:")
    missing_summary = df_raw.isnull().sum()
    print(missing_summary[missing_summary > 0])

    return df_raw


# Load the data
data_path = "demelted_data.csv"
df_raw = load_and_explore_data(data_path)


## Data Cleaning and Feature Engineering


def clean_and_engineer_features(df_raw):
    """Clean data and engineer features for modeling"""
    logger.info("Cleaning data and engineering features...")

    # Start with a copy
    df = df_raw.copy()

    # Handle missing values and infinities
    df.replace([np.inf, -np.inf, ""], np.nan, inplace=True)

    # Remove duplicates (each promo repeated for each competitor)
    print(f"Before deduplication: {len(df)} rows")
    df = df.drop_duplicates()
    print(f"After deduplication: {len(df)} rows")

    # Convert date columns
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])

    # Create duration in days
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days

    # Extract brand and pack information from ABI SKU
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    
    # Fix pack type extraction - the parentheses need to be escaped properly
    df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\(12-15\)', regex=True).astype(int)
    df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\(20-24\)', regex=True).astype(int)
    df['Pack_Type'] = np.where(df['Pack_12_15'] == 1, '12-15',
                              np.where(df['Pack_20_24'] == 1, '20-24', 'Other'))
    
    # Debug pack type extraction
    print("Pack type extraction debug:")
    print(f"Sample ABI SKU values: {df['ABI SKU'].head().tolist()}")
    print(f"Pack_12_15 count: {df['Pack_12_15'].sum()}")
    print(f"Pack_20_24 count: {df['Pack_20_24'].sum()}")
    print(f"Pack types found: {df['Pack_Type'].value_counts()}")
    print(f"KSM count: {df['KSM'].value_counts()}")

    # Create timing variables as requested
    # df['Before'] = ((df['1 wk before'].fillna(0) == 1) |
    #                (df['2 wk before'].fillna(0) == 1)).astype(int)
    # df['After'] = ((df['1 wk after'].fillna(0) == 1) |
    #               (df['2 wk after'].fillna(0) == 1)).astype(int)
    df['before_1_wk'] = df['1 wk before'].fillna(0).astype(int)
    df['before_2_wk'] = df['2 wk before'].fillna(0).astype(int)
    df['after_1_wk'] = df['1 wk after'].fillna(0).astype(int)
    df['after_2_wk'] = df['2 wk after'].fillna(0).astype(int)
    df['Same_Week'] = df['Same Week'].fillna(0).astype(int)


    #new list  = 'before_1_wk', 'before_2_wk', 'after_1_wk', 'after_2_wk', 'Same_Week'

    # Convert depth buckets to numeric (midpoint)
    depth_mapping = {
        '<20%': 0.15,
        '21%-25%': 0.23,
        '26%-30%': 0.28,
        '31%-33%': 0.32,
        '34%+': 0.36
    }
    df['ABI_Depth_Numeric'] = df['ABI Depth'].map(depth_mapping)

    # Clean and prepare key variables
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    # df['Overlapping'] = pd.to_numeric(df['Overlapping'], errors='coerce').fillna(0)
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    df['overlapping_days'] = pd.to_numeric(df['overlapping days'], errors='coerce')

    # Target variable - handle infinities
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')

    # Remove rows with missing target variable
    df = df.dropna(subset=['ABI_MS_Uplift_Rel'])

    # Handle extreme outliers in target (cap at 95th percentile * 3)
    uplift_95 = df['ABI_MS_Uplift_Rel'].quantile(0.95)
    df['ABI_MS_Uplift_Rel'] = np.where(df['ABI_MS_Uplift_Rel'] > uplift_95 * 3,
                                      uplift_95 * 3, df['ABI_MS_Uplift_Rel'])

    # Convert categorical variables
    df['Retailer'] = df['Retailer'].astype('category')
    df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')
    df['Brand'] = df['Brand'].astype('category')
    df['Pack_Type'] = df['Pack_Type'].astype('category')
    df['KSM'] = df['KSM'].astype('category')

    # Standardize continuous variables for better convergence
    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg', 'overlapping_days']
    scaler = StandardScaler()

    for var in continuous_vars:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])

    print("\n" + "="*80)
    print("FEATURE ENGINEERING SUMMARY")
    print("="*80)
    print(f"Final dataset shape: {df.shape}")
    print(f"Brands: {df['Brand'].value_counts()}")
    print(f"Retailers: {df['Retailer'].value_counts()}")
    print(f"Pack Types: {df['Pack_Type'].value_counts()}")
    print(f"Mechanics: {df['ABI_Mechanic'].value_counts()}")
    print(f"KSM: {df['KSM'].value_counts()}")

    return df


# Test the pack type extraction with the fixed regex
print("Testing pack type extraction...")
df_test = clean_and_engineer_features(df_raw)

# Show pack type distribution
print(f"\nPack Type Distribution:")
print(df_test['Pack_Type'].value_counts())

# Show some examples of pack type extraction
print(f"\nSample pack type assignments:")
sample_df = df_test[['ABI SKU', 'Pack_Type', 'Brand']].drop_duplicates().head(10)
print(sample_df.to_string(index=False))

# Group-level analysis by pack type
print(f"\nUplift by Pack Type:")
pack_uplift = df_test.groupby('Pack_Type')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std', 'min', 'max'])
print(pack_uplift)


# Clean and engineer features
df_clean = clean_and_engineer_features(df_raw)


## Exploratory Data Analysis


def perform_eda(df):
    """Perform exploratory data analysis"""
    logger.info("Performing exploratory data analysis...")

    print("\n" + "="*80)
    print("EXPLORATORY DATA ANALYSIS")
    print("="*80)

    # Target variable distribution
    print("\nTarget Variable (ABI_MS_Uplift_Rel) Summary:")
    print(df['ABI_MS_Uplift_Rel'].describe())

    # Create visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # Target distribution
    axes[0,0].hist(df['ABI_MS_Uplift_Rel'], bins=50, alpha=0.7)
    axes[0,0].set_title('Distribution of ABI MS Uplift (Relative)')
    axes[0,0].set_xlabel('Uplift Relative')
    axes[0,0].set_ylabel('Frequency')

    # Duration vs Uplift
    axes[0,1].scatter(df['ABI_Duration_Days'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[0,1].set_title('Duration vs Uplift')
    axes[0,1].set_xlabel('Duration (Days)')
    axes[0,1].set_ylabel('Uplift Relative')

    # Coverage vs Uplift
    axes[0,2].scatter(df['ABI_Coverage'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[0,2].set_title('Coverage vs Uplift')
    axes[0,2].set_xlabel('Coverage')
    axes[0,2].set_ylabel('Uplift Relative')

    # Temperature vs Uplift
    axes[1,0].scatter(df['Avg_Temp'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[1,0].set_title('Temperature vs Uplift')
    axes[1,0].set_xlabel('Average Temperature')
    axes[1,0].set_ylabel('Uplift Relative')

    # KSM vs Uplift
    axes[1,1].scatter(df['KSM'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[1,1].set_title('KSM vs Uplift')
    axes[1,1].set_xlabel('KSM')
    axes[1,1].set_ylabel('Uplift Relative')

    # Box plot: Uplift by Pack Type
    if df['Pack_Type'].nunique() > 1:
        df.boxplot(column='ABI_MS_Uplift_Rel', by='Pack_Type', ax=axes[1,1])
        axes[1,1].set_title('Uplift by Pack Type')
        axes[1,1].set_xlabel('Pack Type')
        axes[1,1].set_ylabel('Uplift Relative')
    
    else:
        axes[1,1].text(0.5, 0.5, 'Pack Type analysis\nnot available\n(single pack type)', 
                      ha='center', va='center', transform=axes[1,1].transAxes)
        axes[1,1].set_title('Pack Type Analysis')

    # Box plot: Uplift by Brand
    df.boxplot(column='ABI_MS_Uplift_Rel', by='Brand', ax=axes[1,2])
    axes[1,2].set_title('Uplift by Brand')
    axes[1,2].set_xlabel('Brand')
    axes[1,2].set_ylabel('Uplift Relative')
   

    plt.tight_layout()
    #plt.savefig('eda_plots.png', dpi=300, bbox_inches='tight')
    plt.show()

    # Correlation matrix for continuous variables
    continuous_cols = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days', 'ABI_Coverage',
                      'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']

    corr_matrix = df[continuous_cols].corr()

    plt.figure(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                square=True, fmt='.3f')
    plt.title('Correlation Matrix of Continuous Variables')
    plt.tight_layout()
    #plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()

    # Group-level summaries
    print("\nGroup-level summaries:")
    print("\nBy Retailer:")
    retailer_summary = df.groupby('Retailer')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(retailer_summary)

    print("\nBy Brand:")
    brand_summary = df.groupby('Brand')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(brand_summary)

    print("\nBy Pack Type:")
    pack_summary = df.groupby('Pack_Type')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(pack_summary)

    print("\nBy Mechanic:")
    mechanic_summary = df.groupby('ABI_Mechanic')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(mechanic_summary)

    return df


## Pack Type Integration Summary

**What we've added:**

1. **Fixed Pack Type Extraction**: 
   - Corrected regex pattern to properly identify (12-15) and (20-24) pack types
   - Added debugging output to verify extraction

2. **Enhanced Hierarchical Models**:
   - **Model 4**: Random intercepts by Pack_Type
   - **Model 6**: Three-way crossed random effects (Retailer + Brand + Pack_Type)
   - Models will only run if sufficient pack type variation exists (≥10 observations per group)

3. **Enhanced Visualizations**:
   - Added box plots for uplift by pack type
   - Expanded EDA plots to include pack type analysis

4. **Expected Benefits**:
   - Account for pack size effects on promotion uplift
   - Better model fit by capturing pack-level variation
   - More accurate coefficient estimates
   - Better prediction accuracy

**Pack Type Categories**:
- **12-15**: Smaller pack sizes (12-15 units)
- **20-24**: Larger pack sizes (20-24 units)
- **Other**: Any pack sizes not matching the above patterns


# Perform exploratory data analysis
df_clean = perform_eda(df_clean)


## Hierarchical Model Building


def calculate_aic_bic(model, n_params=None):
    """Calculate AIC and BIC for mixed-effects models"""
    # Check if model has valid AIC and BIC (not NaN)
    if hasattr(model, 'aic') and hasattr(model, 'bic'):
        if not (np.isnan(model.aic) or np.isnan(model.bic)):
            return model.aic, model.bic
    
    # Calculate manually for mixed-effects models
    if hasattr(model, 'llf'):
        llf = model.llf
        n_obs = model.nobs
        
        # Count parameters more carefully for mixed-effects models
        if n_params is None:
            n_params = 0
            
            # Fixed effects parameters
            if hasattr(model, 'params'):
                n_params += len(model.params)
            
            # Random effects variance parameters
            if hasattr(model, 'cov_re'):
                # For random intercepts: 1 variance parameter per random effect
                # For random slopes: additional variance + covariance parameters
                if hasattr(model, 'cov_re_unscaled'):
                    # Count unique variance components
                    cov_re_shape = model.cov_re_unscaled.shape
                    n_params += (cov_re_shape[0] * (cov_re_shape[0] + 1)) // 2
                else:
                    # Simple case: assume 1 variance parameter per random effect
                    n_params += 1
            
            # Residual variance parameter
            if hasattr(model, 'scale'):
                n_params += 1
        
        # Calculate AIC and BIC
        aic = 2 * n_params - 2 * llf
        bic = np.log(n_obs) * n_params - 2 * llf
        
        return aic, bic
    
    return None, None

def build_hierarchical_models(df):
    """Build hierarchical mixed-effects models"""
    logger.info("Building hierarchical mixed-effects models...")

    print("\n" + "="*80)
    print("HIERARCHICAL MODEL DEVELOPMENT")
    print("="*80)

    # Prepare data for modeling - remove any remaining NaN values
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std', 'overlapping_days',
                   'Same_Week', 'before_1_wk', 'before_2_wk', 'after_1_wk', 'after_2_wk', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'ABI_Mechanic', 'Retailer',
                  'Brand', 'Pack_Type', 'KSM']

    df_model = df[model_vars].dropna()
    print(f"Data for modeling: {df_model.shape[0]} observations")

    models = {}

    # Model 1: Simple fixed effects only (baseline)
    print("\n" + "-"*60)
    print("MODEL 1: Fixed Effects Only (Baseline)")
    print("-"*60)

    try:
        formula_fixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std + overlapping_days +
                         + Same_Week + before_1_wk + before_2_wk + after_1_wk + after_2_wk + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic) + KSM"""

        model_fixed = smf.ols(formula_fixed, data=df_model).fit()
        models['fixed_only'] = model_fixed

        print("Fixed Effects Model Summary:")
        print(model_fixed.summary())
        print(f"R-squared: {model_fixed.rsquared:.4f}")
        print(f"AIC: {model_fixed.aic:.2f}")
        print(f"BIC: {model_fixed.bic:.2f}")

    except Exception as e:
        print(f"Error fitting fixed effects model: {e}")

    # Model 2: Random intercepts by Retailer
    print("\n" + "-"*60)
    print("MODEL 2: Random Intercepts by Retailer")
    print("-"*60)

    try:
        formula_mixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std + overlapping_days +
                           Same_Week + before_1_wk + before_2_wk + after_1_wk + after_2_wk + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic) + KSM"""

        model_retailer = MixedLM.from_formula(formula_mixed, df_model,
                                            groups=df_model["Retailer"]).fit()
        models['retailer_intercept'] = model_retailer

        print("Random Intercepts by Retailer Model Summary:")
        print(model_retailer.summary())
        
        # Calculate AIC and BIC for mixed model
        aic, bic = calculate_aic_bic(model_retailer)
        if aic is not None:
            print(f"AIC: {aic:.2f}")
            print(f"BIC: {bic:.2f}")

    except Exception as e:
        print(f"Error fitting retailer random intercepts model: {e}")

    return models, df_model


def build_advanced_hierarchical_models(df_model):
    """Build more complex hierarchical models including Pack_Type"""
    logger.info("Building advanced hierarchical models...")

    models = {}

    # Base formula for all models
    base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std + overlapping_days +
                     Same_Week + before_1_wk + before_2_wk + after_1_wk + after_2_wk + Avg_Temp_std +
                     ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic) + KSM"""

    # Model 3: Random intercepts by Brand
    print("\n" + "-"*60)
    print("MODEL 3: Random Intercepts by Brand")
    print("-"*60)

    try:
        model_brand = MixedLM.from_formula(base_formula, df_model,
                                         groups=df_model["Brand"]).fit()
        models['brand_intercept'] = model_brand

        print("Random Intercepts by Brand Model Summary:")
        print(model_brand.summary())
        
        # Calculate AIC and BIC for mixed model
        aic, bic = calculate_aic_bic(model_brand)
        if aic is not None:
            print(f"AIC: {aic:.2f}")
            print(f"BIC: {bic:.2f}")

    except Exception as e:
        print(f"Error fitting brand random intercepts model: {e}")

    # Model 4: Random intercepts by Pack_Type
    print("\n" + "-"*60)
    print("MODEL 4: Random Intercepts by Pack_Type")
    print("-"*60)

    try:
        # Check if we have enough pack type variation
        pack_counts = df_model['Pack_Type'].value_counts()
        print(f"Pack Type distribution: {pack_counts}")
        
        if len(pack_counts) > 1 and pack_counts.min() >= 10:  # Need at least 10 obs per group
            model_pack = MixedLM.from_formula(base_formula, df_model,
                                            groups=df_model["Pack_Type"]).fit()
            models['pack_intercept'] = model_pack

            print("Random Intercepts by Pack_Type Model Summary:")
            print(model_pack.summary())
            
            # Calculate AIC and BIC for mixed model
            aic, bic = calculate_aic_bic(model_pack)
            if aic is not None:
                print(f"AIC: {aic:.2f}")
                print(f"BIC: {bic:.2f}")
        else:
            print("Insufficient pack type variation for random effects modeling")
            models['pack_intercept'] = None

    except Exception as e:
        print(f"Error fitting pack intercept model: {e}")
        models['pack_intercept'] = None

    # Model 5: Crossed random effects (Retailer + Brand)
    print("\n" + "-"*60)
    print("MODEL 5: Crossed Random Effects (Retailer + Brand)")
    print("-"*60)

    try:
        # Use variance components for crossed random effects
        vc = {"Brand": "0 + C(Brand)", "Retailer": "0 + C(Retailer)"}

        model_crossed = MixedLM.from_formula(base_formula, df_model,
                                           groups=df_model["Brand"],
                                           vc_formula=vc).fit()
        models['brand_retailer_crossed'] = model_crossed

        print("Brand + Retailer Crossed Random Effects Model Summary:")
        print(model_crossed.summary())
        
        # Calculate AIC and BIC for mixed model (crossed effects has more parameters)
        aic, bic = calculate_aic_bic(model_crossed)
        if aic is not None:
            print(f"AIC: {aic:.2f}")
            print(f"BIC: {bic:.2f}")

    except Exception as e:
        print(f"Error fitting brand + retailer crossed random effects model: {e}")

    # Model 6: Crossed random effects (Retailer + Pack_Type)
    print("\n" + "-"*60)
    print("MODEL 6: Crossed Random Effects (Retailer + Pack_Type)")
    print("-"*60)

    try:
        # Check if we have enough pack type variation
        pack_counts = df_model['Pack_Type'].value_counts()
        
        if len(pack_counts) > 1 and pack_counts.min() >= 10:
            # Use variance components for retailer + pack type crossed effects
            vc = {"Retailer": "0 + C(Retailer)", "Pack_Type": "0 + C(Pack_Type)"}

            model_retailer_pack = MixedLM.from_formula(base_formula, df_model,
                                                     groups=df_model["Retailer"],
                                                     vc_formula=vc).fit()
            models['retailer_pack_crossed'] = model_retailer_pack

            print("Retailer + Pack_Type Crossed Random Effects Model Summary:")
            print(model_retailer_pack.summary())
            
            # Calculate AIC and BIC for mixed model
            aic, bic = calculate_aic_bic(model_retailer_pack)
            if aic is not None:
                print(f"AIC: {aic:.2f}")
                print(f"BIC: {bic:.2f}")
        else:
            print("Insufficient pack type variation for retailer + pack type crossed effects")
            models['retailer_pack_crossed'] = None

    except Exception as e:
        print(f"Error fitting retailer + pack type crossed effects model: {e}")
        models['retailer_pack_crossed'] = None

    # Model 7: Crossed random effects (Brand + Pack_Type)
    print("\n" + "-"*60)
    print("MODEL 7: Crossed Random Effects (Brand + Pack_Type)")
    print("-"*60)

    try:
        # Check if we have enough pack type variation
        pack_counts = df_model['Pack_Type'].value_counts()
        
        if len(pack_counts) > 1 and pack_counts.min() >= 10:
            # Use variance components for brand + pack type crossed effects
            vc = {"Brand": "0 + C(Brand)", "Pack_Type": "0 + C(Pack_Type)"}

            model_brand_pack = MixedLM.from_formula(base_formula, df_model,
                                                  groups=df_model["Brand"],
                                                  vc_formula=vc).fit()
            models['brand_pack_crossed'] = model_brand_pack

            print("Brand + Pack_Type Crossed Random Effects Model Summary:")
            print(model_brand_pack.summary())
            
            # Calculate AIC and BIC for mixed model
            aic, bic = calculate_aic_bic(model_brand_pack)
            if aic is not None:
                print(f"AIC: {aic:.2f}")
                print(f"BIC: {bic:.2f}")
        else:
            print("Insufficient pack type variation for brand + pack type crossed effects")
            models['brand_pack_crossed'] = None

    except Exception as e:
        print(f"Error fitting brand + pack type crossed effects model: {e}")
        models['brand_pack_crossed'] = None

    # Model 8: Three-way crossed random effects (Retailer + Brand + Pack_Type)
    print("\n" + "-"*60)
    print("MODEL 8: Three-way Crossed Random Effects (Retailer + Brand + Pack_Type)")
    print("-"*60)

    try:
        # Check if we have enough pack type variation
        pack_counts = df_model['Pack_Type'].value_counts()
        
        if len(pack_counts) > 1 and pack_counts.min() >= 10:
            # Use variance components for three-way crossed random effects
            vc = {"Brand": "0 + C(Brand)", "Retailer": "0 + C(Retailer)", "Pack_Type": "0 + C(Pack_Type)"}

            model_three_way = MixedLM.from_formula(base_formula, df_model,
                                                 groups=df_model["Brand"],
                                                 vc_formula=vc).fit()
            models['three_way_crossed'] = model_three_way

            print("Three-way Crossed Random Effects Model Summary:")
            print(model_three_way.summary())
            
            # Calculate AIC and BIC for mixed model
            aic, bic = calculate_aic_bic(model_three_way)
            if aic is not None:
                print(f"AIC: {aic:.2f}")
                print(f"BIC: {bic:.2f}")
        else:
            print("Insufficient pack type variation for three-way crossed effects")
            models['three_way_crossed'] = None

    except Exception as e:
        print(f"Error fitting three-way crossed effects model: {e}")
        models['three_way_crossed'] = None

    # Model 9: Random slopes (Duration by Retailer)
    print("\n" + "-"*60)
    print("MODEL 9: Random Slopes - Duration by Retailer")
    print("-"*60)

    try:
        # Random slope for duration by retailer
        model_slopes_retailer = MixedLM.from_formula(base_formula, df_model,
                                                   groups=df_model["Retailer"],
                                                   re_formula="~ABI_Duration_Days_std").fit()
        models['duration_slopes_retailer'] = model_slopes_retailer

        print("Random Slopes (Duration by Retailer) Model Summary:")
        print(model_slopes_retailer.summary())
        
        # Calculate AIC and BIC for mixed model
        aic, bic = calculate_aic_bic(model_slopes_retailer)
        if aic is not None:
            print(f"AIC: {aic:.2f}")
            print(f"BIC: {bic:.2f}")

    except Exception as e:
        print(f"Error fitting random slopes (duration by retailer) model: {e}")

    # Model 10: Random slopes (Duration by Brand)
    print("\n" + "-"*60)
    print("MODEL 10: Random Slopes - Duration by Brand")
    print("-"*60)

    try:
        # Random slope for duration by brand
        model_slopes_brand = MixedLM.from_formula(base_formula, df_model,
                                                groups=df_model["Brand"],
                                                re_formula="~ABI_Duration_Days_std").fit()
        models['duration_slopes_brand'] = model_slopes_brand

        print("Random Slopes (Duration by Brand) Model Summary:")
        print(model_slopes_brand.summary())
        
        # Calculate AIC and BIC for mixed model
        aic, bic = calculate_aic_bic(model_slopes_brand)
        if aic is not None:
            print(f"AIC: {aic:.2f}")
            print(f"BIC: {bic:.2f}")

    except Exception as e:
        print(f"Error fitting random slopes (duration by brand) model: {e}")

    # Model 11: Random slopes (Duration by Pack_Type)
    print("\n" + "-"*60)
    print("MODEL 11: Random Slopes - Duration by Pack_Type")
    print("-"*60)

    try:
        # Check if we have enough pack type variation
        pack_counts = df_model['Pack_Type'].value_counts()
        
        if len(pack_counts) > 1 and pack_counts.min() >= 10:
            # Random slope for duration by pack type
            model_slopes_pack = MixedLM.from_formula(base_formula, df_model,
                                                   groups=df_model["Pack_Type"],
                                                   re_formula="~ABI_Duration_Days_std").fit()
            models['duration_slopes_pack'] = model_slopes_pack

            print("Random Slopes (Duration by Pack_Type) Model Summary:")
            print(model_slopes_pack.summary())
            
            # Calculate AIC and BIC for mixed model
            aic, bic = calculate_aic_bic(model_slopes_pack)
            if aic is not None:
                print(f"AIC: {aic:.2f}")
                print(f"BIC: {bic:.2f}")
        else:
            print("Insufficient pack type variation for random slopes modeling")
            models['duration_slopes_pack'] = None

    except Exception as e:
        print(f"Error fitting random slopes (duration by pack type) model: {e}")
        models['duration_slopes_pack'] = None

    # Model 12: Random slopes (Coverage by Retailer) 
    print("\n" + "-"*60)
    print("MODEL 12: Random Slopes - Coverage by Retailer")
    print("-"*60)

    try:
        # Random slope for coverage by retailer
        model_slopes_cov_retailer = MixedLM.from_formula(base_formula, df_model,
                                                       groups=df_model["Retailer"],
                                                       re_formula="~ABI_Coverage_std").fit()
        models['coverage_slopes_retailer'] = model_slopes_cov_retailer

        print("Random Slopes (Coverage by Retailer) Model Summary:")
        print(model_slopes_cov_retailer.summary())
        
        # Calculate AIC and BIC for mixed model
        aic, bic = calculate_aic_bic(model_slopes_cov_retailer)
        if aic is not None:
            print(f"AIC: {aic:.2f}")
            print(f"BIC: {bic:.2f}")

    except Exception as e:
        print(f"Error fitting random slopes (coverage by retailer) model: {e}")

    return models


## Complete Model Architecture with Pack Type

**We now have a comprehensive hierarchical modeling framework that tests ALL possible combinations:**

### Model Structure Overview:

1. **MODEL 1**: Fixed Effects Only (Baseline)
   - No random effects, serves as baseline comparison

2. **MODEL 2**: Random Intercepts by Retailer
   - Accounts for retailer-specific baseline differences

3. **MODEL 3**: Random Intercepts by Brand  
   - Accounts for brand-specific baseline differences

4. **MODEL 4**: Random Intercepts by Pack_Type
   - Accounts for pack size-specific baseline differences

5. **MODEL 5**: Brand + Retailer Crossed Random Effects
   - Accounts for both retailer AND brand effects simultaneously

6. **MODEL 6**: Crossed Random Effects (Retailer + Pack_Type)
   - Accounts for both retailer AND pack type effects simultaneously

7. **MODEL 7**: Crossed Random Effects (Brand + Pack_Type)
   - Accounts for both brand AND pack type effects simultaneously

8. **MODEL 8**: Three-way Crossed Random Effects (Retailer + Brand + Pack_Type)
   - Most complex crossed effects model accounting for all three grouping variables

**Random Slopes Models:**

9. **MODEL 9**: Random Slopes - Duration by Retailer
   - Allows promotion duration effects to vary by retailer

10. **MODEL 10**: Random Slopes - Duration by Brand
    - Allows promotion duration effects to vary by brand

11. **MODEL 11**: Random Slopes - Duration by Pack_Type
    - Allows promotion duration effects to vary by pack type

12. **MODEL 12**: Random Slopes - Coverage by Retailer
    - Allows promotion coverage effects to vary by retailer

### Key Features:

- **Smart Model Selection**: Models only run if sufficient variation exists (≥10 observations per group)
- **Comprehensive Comparison**: AIC/BIC comparison to find optimal model structure
- **Pack Type Integration**: Now includes pack size effects in the hierarchical structure
- **Robust Error Handling**: Models that fail to converge are safely handled

### Expected Outcomes:

1. **Best Model Identification**: AIC/BIC will identify which combination provides best fit
2. **Effect Decomposition**: Understand relative importance of retailer vs brand vs pack type effects
3. **Improved Predictions**: More accurate promotion uplift predictions
4. **Practical Insights**: Better understanding of how different factors interact


# Build basic hierarchical models
models_basic, df_model = build_hierarchical_models(df_clean)


# Build advanced hierarchical models
models_advanced = build_advanced_hierarchical_models(df_model)

# Combine all models
all_models = {**models_basic, **models_advanced}


## Model Diagnostics and Validation


def model_diagnostics(models, df_model):
    """Perform model diagnostics and validation"""
    logger.info("Performing model diagnostics...")

    print("\n" + "="*80)
    print("MODEL DIAGNOSTICS AND VALIDATION")
    print("="*80)

    for model_name, model in models.items():
        if model is None:
            continue

        print(f"\n{'-'*60}")
        print(f"DIAGNOSTICS FOR {model_name.upper()}")
        print(f"{'-'*60}")

        try:
            # Get fitted values and residuals
            if hasattr(model, 'fittedvalues'):
                fitted = model.fittedvalues
                residuals = model.resid
            else:
                # For mixed models
                fitted = model.fittedvalues
                residuals = model.resid

            # Residual plots
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))

            # Residuals vs Fitted
            axes[0,0].scatter(fitted, residuals, alpha=0.6)
            axes[0,0].axhline(y=0, color='red', linestyle='--')
            axes[0,0].set_xlabel('Fitted Values')
            axes[0,0].set_ylabel('Residuals')
            axes[0,0].set_title(f'{model_name}: Residuals vs Fitted')

            # Q-Q plot
            stats.probplot(residuals, dist="norm", plot=axes[0,1])
            axes[0,1].set_title(f'{model_name}: Q-Q Plot')

            # Histogram of residuals
            axes[1,0].hist(residuals, bins=30, alpha=0.7)
            axes[1,0].set_xlabel('Residuals')
            axes[1,0].set_ylabel('Frequency')
            axes[1,0].set_title(f'{model_name}: Residual Distribution')

            # Scale-Location plot
            axes[1,1].scatter(fitted, np.sqrt(np.abs(residuals)), alpha=0.6)
            axes[1,1].set_xlabel('Fitted Values')
            axes[1,1].set_ylabel('√|Residuals|')
            axes[1,1].set_title(f'{model_name}: Scale-Location')

            plt.tight_layout()
            #plt.savefig(f'diagnostics_{model_name}.png', dpi=300, bbox_inches='tight')
            plt.show()

            # Model fit statistics - use calculate_aic_bic for consistent results
            aic, bic = calculate_aic_bic(model)
            if aic is not None:
                print(f"AIC: {aic:.2f}")
            if bic is not None:
                print(f"BIC: {bic:.2f}")
            if hasattr(model, 'llf'):
                print(f"Log-Likelihood: {model.llf:.2f}")

            # For mixed models, calculate ICC
            if hasattr(model, 'cov_re'):
                try:
                    var_random = np.diag(model.cov_re).sum()
                    var_residual = model.scale
                    icc = var_random / (var_random + var_residual)
                    print(f"Intraclass Correlation (ICC): {icc:.4f}")
                except:
                    print("Could not calculate ICC")

        except Exception as e:
            print(f"Error in diagnostics for {model_name}: {e}")


def interpret_results(models):
    """Interpret and summarize model results"""
    logger.info("Interpreting model results...")

    print("\n" + "="*80)
    print("MODEL RESULTS INTERPRETATION")
    print("="*80)

    # Model comparison
    print("\nMODEL COMPARISON:")
    print("-" * 40)

    comparison_data = []
    for name, model in models.items():
        if model is not None:
            try:
                aic, bic = calculate_aic_bic(model)
                llf = getattr(model, 'llf', 'N/A')
                comparison_data.append({
                    'Model': name,
                    'AIC': aic if aic is not None else 'N/A',
                    'BIC': bic if bic is not None else 'N/A',
                    'Log-Likelihood': llf
                })
            except:
                pass

    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False))

    # Find best model based on AIC
    best_model = None
    best_model_name = None
    best_aic = float('inf')
    
    for name, model in models.items():
        if model is not None:
            try:
                aic, _ = calculate_aic_bic(model)
                if aic is not None and aic < best_aic and np.isfinite(aic):
                    best_aic = aic
                    best_model = model
                    best_model_name = name
            except:
                continue

    if best_model is not None:
        print(f"\nBest model based on AIC: {best_model_name}")

    # Fixed effects interpretation
    if best_model is not None:
        print("\nFIXED EFFECTS INTERPRETATION:")
        print("-" * 30)

        if hasattr(best_model, 'params'):
            params = best_model.params
            pvalues = best_model.pvalues if hasattr(best_model, 'pvalues') else None

            for param, coef in params.items():
                significance = ""
                if pvalues is not None and param in pvalues:
                    p_val = pvalues[param]
                    if p_val < 0.001:
                        significance = "***"
                    elif p_val < 0.01:
                        significance = "**"
                    elif p_val < 0.05:
                        significance = "*"
                    elif p_val < 0.1:
                        significance = "."

                print(f"{param}: {coef:.4f} {significance}")

        # Random effects interpretation (if applicable)
        if hasattr(best_model, 'random_effects'):
            print("\nRANDOM EFFECTS:")
            print("-" * 15)
            try:
                random_effects = best_model.random_effects
                for group, effects in random_effects.items():
                    print(f"\nGroup {group}:")
                    for effect_name, effect_value in effects.items():
                        print(f"  {effect_name}: {effect_value:.4f}")
            except ValueError as e:
                print(f"Could not extract random effects: {e}")
                print("Model has singular covariance structure.")
            except:
                print("Could not extract random effects details")

# Perform model diagnostics
model_diagnostics(all_models, df_model)


# Perform model diagnostics
model_diagnostics(all_models, df_model)


# Perform model diagnostics
model_diagnostics(all_models, df_model)


## Results Interpretation and Summary


# Interpret and summarize results
interpret_results(all_models)


def create_model_comparison_table(models):
    """Create a comprehensive model comparison table with AIC, BIC, and other metrics"""
    print("\n" + "="*80)
    print("COMPREHENSIVE MODEL COMPARISON TABLE")
    print("="*80)
    
    comparison_data = []
    
    for name, model in models.items():
        if model is not None:
            try:
                # Calculate AIC and BIC
                aic, bic = calculate_aic_bic(model)
                
                # Get log-likelihood
                llf = getattr(model, 'llf', None)
                
                # Get number of observations
                n_obs = getattr(model, 'nobs', 'N/A')
                
                # Get number of parameters
                n_params = len(model.params) if hasattr(model, 'params') else 'N/A'
                
                # For mixed models, add variance components count
                if hasattr(model, 'cov_re') and hasattr(model, 'scale'):
                    n_params += 2  # random effect variance + residual variance
                
                # Calculate delta AIC and BIC (difference from best model)
                delta_aic = 'N/A'
                delta_bic = 'N/A'
                
                comparison_data.append({
                    'Model': name,
                    'N_Obs': n_obs,
                    'N_Params': n_params,
                    'Log-Likelihood': f"{llf:.2f}" if llf is not None else 'N/A',
                    'AIC': f"{aic:.2f}" if aic is not None else 'N/A',
                    'BIC': f"{bic:.2f}" if bic is not None else 'N/A',
                    'Delta_AIC': delta_aic,
                    'Delta_BIC': delta_bic,
                    'AIC_Value': aic,  # For sorting purposes
                    'BIC_Value': bic   # For sorting purposes
                })
            except Exception as e:
                print(f"Error processing model {name}: {e}")
                
    # Create DataFrame
    df_comparison = pd.DataFrame(comparison_data)
    
    # Calculate delta AIC and BIC
    if len(df_comparison) > 0:
        # Find best (lowest) AIC and BIC
        valid_aic = df_comparison['AIC_Value'].dropna()
        valid_bic = df_comparison['BIC_Value'].dropna()
        
        if len(valid_aic) > 0:
            best_aic = valid_aic.min()
            df_comparison['Delta_AIC'] = df_comparison['AIC_Value'].apply(
                lambda x: f"{x - best_aic:.2f}" if pd.notnull(x) else 'N/A'
            )
            
        if len(valid_bic) > 0:
            best_bic = valid_bic.min()
            df_comparison['Delta_BIC'] = df_comparison['BIC_Value'].apply(
                lambda x: f"{x - best_bic:.2f}" if pd.notnull(x) else 'N/A'
            )
    
    # Sort by AIC (best first)
    df_comparison = df_comparison.sort_values('AIC_Value', na_position='last')
    
    # Drop the helper columns
    df_comparison = df_comparison.drop(['AIC_Value', 'BIC_Value'], axis=1)
    
    # Display the table
    print("\nModel Comparison (sorted by AIC):")
    print("-" * 40)
    print(df_comparison.to_string(index=False))
    
    # Add interpretation guide
    print("\n" + "-" * 80)
    print("INTERPRETATION GUIDE:")
    print("-" * 80)
    print("• Lower AIC/BIC values indicate better model fit")
    print("• Delta AIC/BIC: Difference from best model")
    print("  - Δ < 2: Substantial support")
    print("  - 2 ≤ Δ < 4: Considerable support")
    print("  - 4 ≤ Δ < 7: Less support")
    print("  - Δ ≥ 7: No support")
    print("• AIC balances fit and complexity (good for prediction)")
    print("• BIC penalizes complexity more heavily (good for model selection)")
    
    return df_comparison

# Create comprehensive model comparison
model_comparison_table = create_model_comparison_table(all_models)


## Test the AIC/BIC calculation fix

# Let's test if our fixed function works with a simple mixed model
print("Testing AIC/BIC calculation fix...")

# Test with existing models
if 'all_models' in locals():
    for model_name, model in all_models.items():
        if model is not None:
            print(f"\n{model_name}:")
            print(f"  Model type: {type(model)}")
            
            # Check raw attributes
            if hasattr(model, 'aic'):
                print(f"  Raw AIC: {model.aic}")
            if hasattr(model, 'bic'):
                print(f"  Raw BIC: {model.bic}")
            if hasattr(model, 'llf'):
                print(f"  Log-likelihood: {model.llf}")
                
            # Test our function
            aic, bic = calculate_aic_bic(model)
            print(f"  Calculated AIC: {aic}")
            print(f"  Calculated BIC: {bic}")
            
            # Debug parameter counting
            if hasattr(model, 'params'):
                print(f"  Fixed effects params: {len(model.params)}")
            if hasattr(model, 'cov_re'):
                print(f"  Has random effects covariance matrix")
                if hasattr(model, 'cov_re_unscaled'):
                    print(f"  Random effects shape: {model.cov_re_unscaled.shape}")
            if hasattr(model, 'scale'):
                print(f"  Has residual variance")
else:
    print("Models not found. Please run the model building cells first.")


def calculate_model_accuracy_metrics(models, df_model):
    """Calculate comprehensive accuracy metrics for all models"""
    print("Calculating accuracy metrics for all models...")
    
    # Store all metrics
    accuracy_results = []
    
    for model_name, model in models.items():
        if model is None:
            continue
            
        try:
            print(f"\nCalculating metrics for {model_name}...")
            
            # Get predictions and actual values
            if hasattr(model, 'fittedvalues'):
                y_pred = model.fittedvalues
                y_true = model.model.endog  # Actual values
                residuals = model.resid
            else:
                print(f"Cannot extract predictions from {model_name}")
                continue
            
            # Basic accuracy metrics
            mae = np.mean(np.abs(y_true - y_pred))
            rmse = np.sqrt(np.mean((y_true - y_pred)**2))
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            residual_std = np.std(residuals)
            
            # R-squared equivalents for mixed models
            ss_res = np.sum((y_true - y_pred) ** 2)
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            pseudo_r2 = 1 - (ss_res / ss_tot)
            
            # For mixed models, try to calculate conditional and marginal R²
            conditional_r2 = None
            marginal_r2 = None
            
            if hasattr(model, 'cov_re') and hasattr(model, 'scale'):
                # This is a mixed model
                try:
                    # Variance of fitted values (total model prediction variance)
                    var_fitted = np.var(y_pred)
                    # Total variance in the data
                    var_total = np.var(y_true)
                    # Conditional R² (fixed + random effects)
                    conditional_r2 = var_fitted / var_total
                    
                    # For marginal R², we need fixed effects only predictions
                    # This is an approximation
                    marginal_r2 = pseudo_r2
                    
                except:
                    conditional_r2 = pseudo_r2
                    marginal_r2 = pseudo_r2
            else:
                # This is a fixed effects model
                marginal_r2 = pseudo_r2
                conditional_r2 = pseudo_r2
            
            # Model complexity metrics
            n_params = len(model.params) if hasattr(model, 'params') else 'N/A'
            aic, bic = calculate_aic_bic(model)
            
            # Store results
            accuracy_results.append({
                'Model': model_name,
                'MAE': mae,
                'RMSE': rmse,
                'MAPE': mape,
                'Pseudo_R2': pseudo_r2,
                'Conditional_R2': conditional_r2,
                'Marginal_R2': marginal_r2,
                'Residual_Std': residual_std,
                'N_Params': n_params,
                'AIC': aic,
                'BIC': bic
            })
            
        except Exception as e:
            print(f"Error calculating metrics for {model_name}: {e}")
            continue
    
    return pd.DataFrame(accuracy_results)

# Calculate accuracy metrics for all models
accuracy_df = calculate_model_accuracy_metrics(all_models, df_model)
print("\n" + "="*100)
print("MODEL ACCURACY COMPARISON")
print("="*100)
print(accuracy_df.round(4))


# Create comprehensive accuracy visualization
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Model Accuracy Metrics Comparison', fontsize=16, fontweight='bold')

# Filter out rows with None values for plotting
plot_df = accuracy_df.dropna()

# 1. MAE comparison
ax1 = axes[0, 0]
bars1 = ax1.bar(range(len(plot_df)), plot_df['MAE'], color='skyblue', alpha=0.7)
ax1.set_title('Mean Absolute Error (MAE)\n(Lower is Better)', fontweight='bold')
ax1.set_xlabel('Model')
ax1.set_ylabel('MAE')
ax1.set_xticks(range(len(plot_df)))
ax1.set_xticklabels(plot_df['Model'], rotation=45, ha='right')
# Add value labels on bars
for bar, value in zip(bars1, plot_df['MAE']):
    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
             f'{value:.3f}', ha='center', va='bottom', fontsize=8)

# 2. RMSE comparison
ax2 = axes[0, 1]
bars2 = ax2.bar(range(len(plot_df)), plot_df['RMSE'], color='lightcoral', alpha=0.7)
ax2.set_title('Root Mean Square Error (RMSE)\n(Lower is Better)', fontweight='bold')
ax2.set_xlabel('Model')
ax2.set_ylabel('RMSE')
ax2.set_xticks(range(len(plot_df)))
ax2.set_xticklabels(plot_df['Model'], rotation=45, ha='right')
for bar, value in zip(bars2, plot_df['RMSE']):
    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
             f'{value:.3f}', ha='center', va='bottom', fontsize=8)

# 3. MAPE comparison
ax3 = axes[0, 2]
bars3 = ax3.bar(range(len(plot_df)), plot_df['MAPE'], color='lightgreen', alpha=0.7)
ax3.set_title('Mean Absolute Percentage Error (MAPE)\n(Lower is Better)', fontweight='bold')
ax3.set_xlabel('Model')
ax3.set_ylabel('MAPE (%)')
ax3.set_xticks(range(len(plot_df)))
ax3.set_xticklabels(plot_df['Model'], rotation=45, ha='right')
for bar, value in zip(bars3, plot_df['MAPE']):
    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
             f'{value:.1f}%', ha='center', va='bottom', fontsize=8)

# 4. Pseudo R² comparison
ax4 = axes[1, 0]
bars4 = ax4.bar(range(len(plot_df)), plot_df['Pseudo_R2'], color='gold', alpha=0.7)
ax4.set_title('Pseudo R² (Explained Variance)\n(Higher is Better)', fontweight='bold')
ax4.set_xlabel('Model')
ax4.set_ylabel('Pseudo R²')
ax4.set_xticks(range(len(plot_df)))
ax4.set_xticklabels(plot_df['Model'], rotation=45, ha='right')
for bar, value in zip(bars4, plot_df['Pseudo_R2']):
    ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
             f'{value:.3f}', ha='center', va='bottom', fontsize=8)

# 5. AIC comparison (if available)
ax5 = axes[1, 1]
if not plot_df['AIC'].isna().all():
    bars5 = ax5.bar(range(len(plot_df)), plot_df['AIC'], color='mediumpurple', alpha=0.7)
    ax5.set_title('Akaike Information Criterion (AIC)\n(Lower is Better)', fontweight='bold')
    ax5.set_xlabel('Model')
    ax5.set_ylabel('AIC')
    ax5.set_xticks(range(len(plot_df)))
    ax5.set_xticklabels(plot_df['Model'], rotation=45, ha='right')
    for bar, value in zip(bars5, plot_df['AIC']):
        if not pd.isna(value):
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10, 
                     f'{value:.0f}', ha='center', va='bottom', fontsize=8)
else:
    ax5.text(0.5, 0.5, 'AIC values not available', ha='center', va='center', transform=ax5.transAxes)
    ax5.set_title('AIC Not Available')

# 6. Model complexity (Number of parameters)
ax6 = axes[1, 2]
if not plot_df['N_Params'].apply(lambda x: x == 'N/A').all():
    bars6 = ax6.bar(range(len(plot_df)), plot_df['N_Params'], color='orange', alpha=0.7)
    ax6.set_title('Model Complexity\n(Number of Parameters)', fontweight='bold')
    ax6.set_xlabel('Model')
    ax6.set_ylabel('Number of Parameters')
    ax6.set_xticks(range(len(plot_df)))
    ax6.set_xticklabels(plot_df['Model'], rotation=45, ha='right')
    for bar, value in zip(bars6, plot_df['N_Params']):
        if value != 'N/A':
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                     f'{value}', ha='center', va='bottom', fontsize=8)
else:
    ax6.text(0.5, 0.5, 'Parameter counts not available', ha='center', va='center', transform=ax6.transAxes)
    ax6.set_title('Parameter Counts Not Available')

plt.tight_layout()
#plt.savefig('model_accuracy_comparison.png', dpi=300, bbox_inches='tight')
plt.show()


# Create a ranking table based on different criteria
print("\n" + "="*120)
print("MODEL RANKING SUMMARY")
print("="*120)

# Create separate rankings for different metrics
ranking_df = accuracy_df.copy()

# Rank models (1 = best)
ranking_df['MAE_Rank'] = ranking_df['MAE'].rank(ascending=True)  # Lower is better
ranking_df['RMSE_Rank'] = ranking_df['RMSE'].rank(ascending=True)  # Lower is better
ranking_df['MAPE_Rank'] = ranking_df['MAPE'].rank(ascending=True)  # Lower is better
ranking_df['R2_Rank'] = ranking_df['Pseudo_R2'].rank(ascending=False)  # Higher is better

# Calculate overall ranking (average of all ranks)
ranking_df['Overall_Rank'] = (ranking_df['MAE_Rank'] + ranking_df['RMSE_Rank'] + 
                             ranking_df['MAPE_Rank'] + ranking_df['R2_Rank']) / 4

# Sort by overall ranking
ranking_df = ranking_df.sort_values('Overall_Rank')

# Display simplified ranking table
ranking_display = ranking_df[['Model', 'MAE', 'RMSE', 'MAPE', 'Pseudo_R2', 'Overall_Rank']].round(4)
print(ranking_display)

print("\n" + "="*120)
print("BEST PERFORMING MODELS")
print("="*120)

# Find best model for each metric
best_mae = ranking_df.loc[ranking_df['MAE'].idxmin()]
best_rmse = ranking_df.loc[ranking_df['RMSE'].idxmin()]
best_mape = ranking_df.loc[ranking_df['MAPE'].idxmin()]
best_r2 = ranking_df.loc[ranking_df['Pseudo_R2'].idxmax()]
best_overall = ranking_df.iloc[0]  # Already sorted by overall rank

print(f"Best MAE (Lowest):     {best_mae['Model']:35} (MAE: {best_mae['MAE']:.4f})")
print(f"Best RMSE (Lowest):    {best_rmse['Model']:35} (RMSE: {best_rmse['RMSE']:.4f})")
print(f"Best MAPE (Lowest):    {best_mape['Model']:35} (MAPE: {best_mape['MAPE']:.2f}%)")
print(f"Best R² (Highest):     {best_r2['Model']:35} (R²: {best_r2['Pseudo_R2']:.4f})")
print(f"Best Overall:          {best_overall['Model']:35} (Overall Rank: {best_overall['Overall_Rank']:.2f})")

print("\n" + "="*120)
print("INSIGHTS AND RECOMMENDATIONS")
print("="*120)

# Calculate some insights
mae_improvement = ((ranking_df['MAE'].max() - ranking_df['MAE'].min()) / ranking_df['MAE'].max()) * 100
rmse_improvement = ((ranking_df['RMSE'].max() - ranking_df['RMSE'].min()) / ranking_df['RMSE'].max()) * 100
r2_range = ranking_df['Pseudo_R2'].max() - ranking_df['Pseudo_R2'].min()

print(f"• The best model achieves {mae_improvement:.1f}% lower MAE compared to the worst model")
print(f"• RMSE improvement from worst to best model: {rmse_improvement:.1f}%")
print(f"• Range in explained variance (R²): {r2_range:.4f}")
print(f"• Best overall model: '{best_overall['Model']}'")

# Check if hierarchical models are better than fixed effects
fixed_model_score = ranking_df[ranking_df['Model'] == 'fixed_only']['Overall_Rank'].values
if len(fixed_model_score) > 0:
    hierarchical_better = (ranking_df['Overall_Rank'] < fixed_model_score[0]).sum() - 1  # -1 to exclude fixed model itself
    print(f"• {hierarchical_better} hierarchical models outperform the fixed-effects-only model")
else:
    print("• Fixed-effects-only model not found in results")


# Fix for cross-validation - redefine with correct column names

def cross_validate_model_fixed(df, formula, groups=None, n_splits=5, model_type='fixed'):
    """Perform cross-validation for hierarchical models with correct column names"""
    from sklearn.model_selection import KFold
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    
    kf = KFold(n_splits=n_splits, shuffle=True, random_state=42)
    cv_scores = {'mae': [], 'rmse': [], 'mape': []}
    
    for train_idx, test_idx in kf.split(df):
        try:
            train_data = df.iloc[train_idx]
            test_data = df.iloc[test_idx]
            
            if model_type == 'fixed':
                # Fixed effects model
                model = smf.ols(formula, data=train_data).fit()
                y_pred = model.predict(test_data)
            else:
                # Mixed effects model
                if groups is None:
                    continue
                    
                # Check if group column exists in both train and test
                if groups not in train_data.columns or groups not in test_data.columns:
                    print(f"  Group column {groups} not found")
                    continue
                    
                model = smf.mixedlm(formula, data=train_data, groups=train_data[groups]).fit()
                y_pred = model.predict(test_data)
            
            y_true = test_data['ABI_MS_Uplift_Rel']  # Correct column name
            
            # Calculate metrics
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            
            cv_scores['mae'].append(mae)
            cv_scores['rmse'].append(rmse)
            cv_scores['mape'].append(mape)
            
        except Exception as e:
            print(f"  Cross-validation fold failed: {e}")
            continue
    
    return cv_scores

print("✓ Cross-validation function updated with correct column names")


# Simple column check - run this and share the output
print("DataFrame shape:", df_model.shape)
print("\nAll columns in df_model:")
print(list(df_model.columns))

print("\nTarget variable check:")
target_cols = [col for col in df_model.columns if 'uplift' in col.lower()]
print("Uplift columns:", target_cols)

print("\nSample of first few rows to see data structure:")
print(df_model.head())

## Final Model Selection Recommendation

Based on the comprehensive accuracy assessment above, here's how to interpret the results:

### Key Metrics to Consider:
1. **MAE (Mean Absolute Error)**: Best for understanding average prediction error in original units
2. **RMSE (Root Mean Square Error)**: Penalizes large errors more heavily than MAE
3. **MAPE (Mean Absolute Percentage Error)**: Shows percentage error, good for relative comparison
4. **Pseudo R²**: Proportion of variance explained (higher is better)
5. **Cross-validation**: Most robust indicator of real-world performance

### Model Selection Criteria:
- **For Production Use**: Choose model with lowest cross-validation error
- **For Understanding**: Choose model with highest R² and good interpretability
- **For Robustness**: Choose model that performs consistently across all metrics
- **For Simplicity**: Consider parameter count vs. performance trade-off


# Save validation results for future reference
print("Saving validation results...")

# Save accuracy comparison table
accuracy_df.to_csv('model_accuracy_results.csv', index=False)
print("✓ Saved model accuracy results to 'model_accuracy_results.csv'")

# Save ranking table
ranking_df.to_csv('model_ranking_results.csv', index=False)
print("✓ Saved model ranking results to 'model_ranking_results.csv'")

# Save cross-validation results if available
if cv_results:
    cv_df.to_csv('cross_validation_results.csv')
    print("✓ Saved cross-validation results to 'cross_validation_results.csv'")

print("\n" + "="*100)
print("VALIDATION ANALYSIS COMPLETE!")
print("="*100)
print("All model accuracy metrics have been calculated and saved.")
print("Review the results above to select the best performing model for your use case.")
print("\nFiles generated:")
print("• model_accuracy_comparison.png - Visualization of all accuracy metrics")
print("• model_accuracy_results.csv - Detailed accuracy metrics table")
print("• model_ranking_results.csv - Model rankings by different criteria")
if cv_results:
    print("• cross_validation_results.csv - Cross-validation performance")
print("\nUse these results to make an informed decision about which model to deploy!")


# Now create the correct formula using actual column names
print("Creating correct cross-validation formula using actual columns...")

# Build formula with the exact columns from df_model
correct_formula = '''ABI_MS_Uplift_Rel ~ C(ABI_Mechanic) + ABI_Duration_Days_std + ABI_Coverage_std + 
                     Same_Week + before_1_wk + before_2_wk + after_1_wk + after_2_wk + KSM + Avg_Temp_std +
                     ABI_vs_Segment_PTC_Index_Agg_std'''

print(f"Correct formula: {correct_formula}")

# Test the formula first
print("\nTesting the formula...")
try:
    test_model = smf.ols(correct_formula, data=df_model).fit()
    print(f"✓ Formula works! Model has {len(test_model.params)} parameters")
    print(f"R²: {test_model.rsquared:.4f}")
    print("Formula ready for cross-validation!")
except Exception as e:
    print(f"✗ Formula test failed: {e}")
    print("Will use fallback formula...")
    correct_formula = "ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std"


# Cross-validation for models with fewer groups (Brand and Pack_Type)
print("Running cross-validation for models with fewer groups...")

def cross_validate_few_groups(df, formula, groups=None, model_type='fixed'):
    """Cross-validation adapted for models with few groups"""
    from sklearn.model_selection import KFold, LeaveOneOut
    
    if groups is None:
        # Standard 5-fold for fixed effects
        kf = KFold(n_splits=5, shuffle=True, random_state=42)
        cv_splits = list(kf.split(df))
    else:
        # Handle both single column and list of columns
        if isinstance(groups, list):
            # For three-way crossed: create combined grouping
            df_temp = df.copy()
            df_temp['combined_group'] = df_temp[groups].apply(lambda x: '_'.join(x.astype(str)), axis=1)
            n_groups = df_temp['combined_group'].nunique()
            group_col = 'combined_group'
            print(f"  {'+'.join(groups)} has {n_groups} unique combinations")
        else:
            # Single grouping variable
            n_groups = df[groups].nunique()
            group_col = groups
            print(f"  {groups} has {n_groups} groups")
        
        if n_groups >= 5:
            # Standard 5-fold
            kf = KFold(n_splits=5, shuffle=True, random_state=42)
            cv_splits = list(kf.split(df))
        elif n_groups >= 3:
            # Use 3-fold for 3-4 groups
            kf = KFold(n_splits=3, shuffle=True, random_state=42)
            cv_splits = list(kf.split(df))
        else:
            # Use Leave-One-Out for 2 groups
            loo = LeaveOneOut()
            cv_splits = list(loo.split(df))
    
    cv_scores = {'mae': [], 'rmse': [], 'mape': []}
    
    for train_idx, test_idx in cv_splits:
        try:
            train_data = df.iloc[train_idx].copy()
            test_data = df.iloc[test_idx].copy()
            
            if model_type == 'fixed':
                model = smf.ols(formula, data=train_data).fit()
                y_pred = model.predict(test_data)
            elif model_type == 'mixed_crossed':
                # Handle three-way crossed random effects
                train_data['combined_group'] = train_data[groups].apply(lambda x: '_'.join(x.astype(str)), axis=1)
                test_data['combined_group'] = test_data[groups].apply(lambda x: '_'.join(x.astype(str)), axis=1)
                model = smf.mixedlm(formula, data=train_data, groups=train_data['combined_group']).fit()
                y_pred = model.predict(test_data)
            else:
                # Single group mixed model
                model = smf.mixedlm(formula, data=train_data, groups=train_data[groups]).fit()
                y_pred = model.predict(test_data)
            
            y_true = test_data['ABI_MS_Uplift_Rel']
            
            # Calculate metrics
            mae = np.mean(np.abs(y_true - y_pred))
            rmse = np.sqrt(np.mean((y_true - y_pred)**2))
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            
            cv_scores['mae'].append(mae)
            cv_scores['rmse'].append(rmse)
            cv_scores['mape'].append(mape)
            
        except Exception as e:
            print(f"    Fold failed: {e}")
            continue
    
    return cv_scores

print("\n" + "="*100)
print("COMPLETE CROSS-VALIDATION (INCLUDING BRAND & PACK_TYPE)")
print("="*100)

# Run all models including those with few groups
cv_models_complete = {
    'fixed_only': (correct_formula, None, 'fixed'),
    'retailer_intercept': (correct_formula, 'Retailer', 'mixed'),
    'brand_intercept': (correct_formula, 'Brand', 'mixed'),
    'pack_intercept': (correct_formula, 'Pack_Type', 'mixed'),
    'three_way_crossed': (correct_formula, ['Retailer', 'Brand', 'Pack_Type'], 'mixed_crossed')
}

cv_results_complete = {}
for model_name, (formula, groups, model_type) in cv_models_complete.items():
    print(f"\nCross-validating {model_name}...")
    
    cv_scores = cross_validate_few_groups(df_model, formula, groups, model_type=model_type)
    
    if len(cv_scores['mae']) > 0:
        cv_results_complete[model_name] = {
            'CV_MAE_mean': np.mean(cv_scores['mae']),
            'CV_MAE_std': np.std(cv_scores['mae']),
            'CV_RMSE_mean': np.mean(cv_scores['rmse']),
            'CV_RMSE_std': np.std(cv_scores['rmse']),
            'CV_MAPE_mean': np.mean(cv_scores['mape']),
            'CV_MAPE_std': np.std(cv_scores['mape']),
            'CV_Folds': len(cv_scores['mae'])
        }
        
        print(f"  ✓ MAE: {cv_results_complete[model_name]['CV_MAE_mean']:.4f} ± {cv_results_complete[model_name]['CV_MAE_std']:.4f}")
        print(f"  ✓ RMSE: {cv_results_complete[model_name]['CV_RMSE_mean']:.4f} ± {cv_results_complete[model_name]['CV_RMSE_std']:.4f}")
        print(f"  ✓ MAPE: {cv_results_complete[model_name]['CV_MAPE_mean']:.2f}% ± {cv_results_complete[model_name]['CV_MAPE_std']:.2f}%")
        print(f"  ✓ Successful folds: {cv_results_complete[model_name]['CV_Folds']}")
    else:
        print(f"  ✗ No successful cross-validation folds for {model_name}")

# Display complete results
if cv_results_complete:
    cv_df_complete = pd.DataFrame(cv_results_complete).T
    print("\n" + "="*100)
    print("COMPLETE CROSS-VALIDATION SUMMARY")
    print("="*100)
    print(cv_df_complete.round(4))
    
    # Compare with training accuracy
    print("\n" + "="*100)
    print("TRAINING vs CROSS-VALIDATION COMPARISON")
    print("="*100)
    
    for model in cv_results_complete.keys():
        if model in accuracy_df['Model'].values:
            train_mae = accuracy_df[accuracy_df['Model'] == model]['MAE'].iloc[0]
            cv_mae = cv_results_complete[model]['CV_MAE_mean']
            difference = ((cv_mae - train_mae) / train_mae) * 100
            
            print(f"{model:20}: Train MAE={train_mae:.4f}, CV MAE={cv_mae:.4f}, "
                  f"CV is {difference:+.1f}% vs train")
    
    # Find best CV model
    best_cv_model = min(cv_results_complete.keys(), key=lambda x: cv_results_complete[x]['CV_MAE_mean'])
    print(f"\n🏆 BEST CROSS-VALIDATION MODEL: {best_cv_model}")
    print(f"   CV MAE: {cv_results_complete[best_cv_model]['CV_MAE_mean']:.4f} ± {cv_results_complete[best_cv_model]['CV_MAE_std']:.4f}")
    
    # Rank all models by CV performance
    print(f"\n📊 CROSS-VALIDATION RANKING:")
    cv_ranking = sorted(cv_results_complete.items(), key=lambda x: x[1]['CV_MAE_mean'])
    for i, (model, results) in enumerate(cv_ranking, 1):
        print(f"{i}. {model:20}: CV MAE = {results['CV_MAE_mean']:.4f}")
    
    # Save complete results
    cv_df_complete.to_csv('cross_validation_complete_results.csv')
    print("\n✅ Saved complete cross-validation results to 'cross_validation_complete_results.csv'")
    
else:
    print("\n❌ No successful cross-validation results")

print("\n" + "="*100)
print("COMPLETE CROSS-VALIDATION FINISHED! 🎉")
print("="*100)


