import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

plt.style.use('default')
sns.set_palette("husl")

# Install openpyxl if not already installed for Excel file reading
try:
    import openpyxl
except ImportError:
    print("Installing openpyxl for Excel file support...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl"])
    import openpyxl

##Test

import pandas as pd
trial=pd.read_csv('demelted_data.csv')
print("Column names in trial dataframe:")
print(trial.columns.tolist())


trial.drop(columns=["Retailer","ABI PromoID","ABI SKU","ABI Depth","Competitor SKU", "Competitor Coverage","Competitor Mechanic", "Competitor Depth","ABI MS Promo", "ABI MS Base", "ABI MS Promo Uplift - abs",'base_ms_weeks','ABI Promo PTC/HL Index','Comp Promo PTC/HL','Comp W_Distribution', 'Comp Num_Distribution', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg' ], inplace=True)

trial.columns.tolist()

def load_and_clean_data(file_path):
    df = pd.read_csv(file_path)

    df.drop(columns=["ABI PromoID","ABI SKU","ABI Depth","Competitor SKU", "Competitor Coverage","Competitor Mechanic", "Competitor Depth","ABI MS Promo", "ABI MS Base", "ABI MS Promo Uplift - abs",'base_ms_weeks','ABI Promo PTC/HL Index','Comp Promo PTC/HL','Comp W_Distribution', 'Comp Num_Distribution', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg' ], inplace=True)
    
    # Drop the first column (_c0) if it exists as it's just an index
    if '_c0' in df.columns:
        df = df.drop(columns=['_c0'])
    
    # Convert date columns
    date_cols = ['ABI Start', 'ABI End']
    for col in date_cols:
        df[col] = pd.to_datetime(df[col])
    
    # Calculate promotion duration
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days + 1
    
    # Extract numeric values from percentage columns
    percentage_cols = ['ABI Depth', 'Competitor Depth']
    for col in percentage_cols:
        if col in df.columns:
            df[f'{col}_numeric'] = df[col].str.extract(r'(\d+)').astype(float)
    
    # Parse base_ms_weeks if it exists
    if 'base_ms_weeks' in df.columns:
        df['base_ms_weeks_count'] = df['base_ms_weeks'].str.count(',') + 1
    

    unnamed_cols = [col for col in df.columns if col.lower().startswith('unnamed')]
    if unnamed_cols:
        
        print(f"Dropping unnamed columns: {unnamed_cols}")
        df = df.drop(columns=unnamed_cols)

    

    return df



def basic_statistics(df):
    print("=== BASIC DATA OVERVIEW ===")
    print(f"Dataset shape: {df.shape}")
    print(f"Date range: {df['ABI Start'].min()} to {df['ABI End'].max()}")
    print("\nColumn types:")
    print(df.dtypes)
    print("\nMissing values:")
    print(df.isnull().sum())
    print("\nSAMPLE:")
    print(df.head())
    print("\nBASIC STATS:")
    print(df.describe(include='all'))
    print("\n" + "="*50 + "\n")

def missing_value_analysis(df):
    print("=== MISSING VALUE ANALYSIS ===")
    missing = df.isnull().sum()
    missing_percentage = 100 * missing / len(df)
    missing_df = pd.DataFrame({'Missing Values': missing, 'Percentage': missing_percentage})
    print(missing_df[missing_df['Missing Values'] > 0].sort_values(by='Percentage', ascending=False))
    plt.figure(figsize=(10, 5))
    sns.heatmap(df.isnull(), cbar=False, cmap='viridis')
    plt.title("Missing Values Heatmap")
    plt.tight_layout()
    plt.show()
    print("\n" + "="*50 + "\n")

def detect_outliers(df, z_thresh=3):
    print(f"=== OUTLIER DETECTION (Z > {z_thresh:.1f}) ===")
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    outliers = {}
    for col in numeric_cols:
        col_zscore = (df[col] - df[col].mean())/df[col].std()
        outliers_count = (np.abs(col_zscore) > z_thresh).sum()
        outliers[col] = outliers_count
        print(f"{col}: {outliers_count} potential outliers (Z>{z_thresh})")
        plt.figure(figsize=(8, 4))
        sns.boxplot(x=df[col])
        plt.title(f"Boxplot for {col}")
        plt.tight_layout()
        plt.show()
    print("\n" + "="*50 + "\n")
    return outliers

def promotion_analysis(df):
    print("=== PROMOTION ANALYSIS ===")
    print("Promotion Duration Statistics:")
    print(df['ABI_Duration_Days'].describe())
    print("\nPromotion Mechanics Distribution:")
    print(df['ABI Mechanic'].value_counts())
    print("\nCoverage Distribution:")
    print(df['ABI Coverage'].describe())
    if 'ABI Depth_numeric' in df.columns:
        print("\nPromotion Depth Statistics:")
        print(df['ABI Depth_numeric'].describe())
    print("\n" + "="*50 + "\n")

def competitive_analysis(df):
    print("=== COMPETITIVE ANALYSIS ===")
    overlap_cols = ['Overlapping', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    print("Competitive Timing Analysis:")
    for col in overlap_cols:
        if col in df.columns:
            print(f"{col}: {df[col].sum()} promotions ({df[col].mean()*100:.1f}%)")
    print("\nCompetitor Promotion Mechanics:")
    print(df['Competitor Mechanic'].value_counts())
    if 'ABI Depth_numeric' in df.columns and 'Competitor Depth_numeric' in df.columns:
        print("\nDepth Comparison:")
        print(f"ABI Average Depth: {df['ABI Depth_numeric'].mean():.1f}%")
        print(f"Competitor Average Depth: {df['Competitor Depth_numeric'].mean():.1f}%")
    print("\n" + "="*50 + "\n")

def market_share_analysis(df):
    print("=== MARKET SHARE ANALYSIS ===")
    ms_cols = ['ABI MS Promo', 'ABI MS Base', 'ABI MS Promo Uplift - abs', 'ABI MS Promo Uplift - rel']
    for col in ms_cols:
        if col in df.columns:
            print(f"\n{col} Statistics:")
            print(df[col].describe())
    print("\n" + "="*50 + "\n")

def retailer_analysis(df):
    print("=== RETAILER ANALYSIS ===")
    print("Number of Promotions by Retailer:")
    retailer_counts = df['Retailer'].value_counts()
    print(retailer_counts)
    if 'ABI MS Promo Uplift - rel' in df.columns:
        print("\nAverage Relative Uplift by Retailer:")
        retailer_performance = df.groupby('Retailer')['ABI MS Promo Uplift - rel'].agg(['mean', 'median', 'count'])
        print(retailer_performance)
    print("\n" + "="*50 + "\n")

def seasonal_analysis(df):
    print("=== SEASONAL ANALYSIS ===")
    df['Start_Month'] = df['ABI Start'].dt.month
    df['Start_Quarter'] = df['ABI Start'].dt.quarter
    df['Start_Year'] = df['ABI Start'].dt.year
    print("Promotions by Month:")
    monthly_dist = df['Start_Month'].value_counts().sort_index()
    print(monthly_dist)
    print("\nPromotions by Quarter:")
    quarterly_dist = df['Start_Quarter'].value_counts().sort_index()
    print(quarterly_dist)
    if 'ABI MS Promo Uplift - rel' in df.columns:
        print("\nAverage Uplift by Quarter:")
        quarterly_performance = df.groupby('Start_Quarter')['ABI MS Promo Uplift - rel'].mean()
        print(quarterly_performance)
    print("\n" + "="*50 + "\n")

def correlation_analysis(df, threshold=0.8):
    print("=== CORRELATION ANALYSIS ===")
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    if len(numeric_cols) > 1:
        correlation_matrix = df[numeric_cols].corr()
        uplift_cols = [col for col in numeric_cols if 'Uplift' in col]
        if uplift_cols:
            print("Correlations with Promotion Uplift:")
            for uplift_col in uplift_cols:
                print(f"\n{uplift_col} correlations:")
                corr_with_uplift = correlation_matrix[uplift_col].sort_values(ascending=False)
                print(corr_with_uplift.head(10))
        plt.figure(figsize=(12, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f')
        plt.title("Correlation Matrix")
        plt.tight_layout()
        plt.show()
        print("\n--- HIGH CORRELATION PAIRS (Potential Multicollinearity) ---")
        for i in range(len(correlation_matrix.columns)):
            for j in range(i):
                if abs(correlation_matrix.iloc[i, j]) > threshold:
                    print(f"{correlation_matrix.columns[i]} and {correlation_matrix.columns[j]}: {correlation_matrix.iloc[i, j]:.2f}")
    print("\n" + "="*50 + "\n")

def plot_competitor_overlap_matrix(df, top_n_abi=10, top_n_comp=10):
    """
    Plots a heatmap of number of promo overlaps for each ABI SKU vs Competitor SKU.
    Only the top_n_abi ABI SKUs and top_n_comp competitor SKUs by total overlaps are shown for clarity.
    Shows full SKU names (no truncation), with small font size for axis labels and annotations.
    """
    if 'ABI SKU' not in df.columns or 'Competitor SKU' not in df.columns or 'Overlapping' not in df.columns:
        print("Required columns not found in DataFrame.")
        return
    overlap_df = df[df['Overlapping'] == 1]
    overlap_counts = (
        overlap_df.groupby(['ABI SKU', 'Competitor SKU'])
        .size()
        .reset_index(name='Num_Overlaps')
    )
    top_abi = (
        overlap_counts.groupby('ABI SKU')['Num_Overlaps'].sum()
        .sort_values(ascending=False)
        .head(top_n_abi)
        .index
    )
    top_comp = (
        overlap_counts.groupby('Competitor SKU')['Num_Overlaps'].sum()
        .sort_values(ascending=False)
        .head(top_n_comp)
        .index
    )
    filtered = overlap_counts[
        overlap_counts['ABI SKU'].isin(top_abi) & overlap_counts['Competitor SKU'].isin(top_comp)
    ]
    pivot = filtered.pivot(index='ABI SKU', columns='Competitor SKU', values='Num_Overlaps').fillna(0)
    plt.figure(figsize=(1.5 * len(pivot.columns) + 3, 1.2 * len(pivot.index) + 2))
    ax = sns.heatmap(
        pivot,
        annot=True,
        fmt=".0f",
        cmap="YlGnBu",
        cbar_kws={'label': 'Number of Overlaps'},
        annot_kws={"size": 7}
    )
    plt.title("Promo Overlaps: ABI SKU vs Competitor SKU")
    plt.ylabel("ABI SKU")
    plt.xlabel("Competitor SKU")
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right', fontsize=7)
    ax.set_yticklabels(ax.get_yticklabels(), rotation=0, fontsize=7)
    plt.tight_layout()
    plt.show()

def create_visualizations(df):

    plt.figure(figsize=(8, 5))
    n, bins, patches = plt.hist(df['ABI_Duration_Days'], bins=20, edgecolor='black', alpha=0.7)
    plt.title('Promotion Duration Distribution')
    plt.xlabel('Days')
    plt.ylabel('Frequency')
    for i in range(len(patches)):
        plt.text(patches[i].get_x() + patches[i].get_width()/2, n[i], int(n[i]), 
                 ha='center', va='bottom', fontsize=9)
    plt.tight_layout()
    plt.show()
    # 2. Market Share: Promo vs Base (with data labels, only once)
    if 'ABI MS Promo' in df.columns and 'ABI MS Base' in df.columns:
        plt.figure(figsize=(8, 5))
        plt.scatter(df['ABI MS Base'], df['ABI MS Promo'], alpha=0.6)
        plt.plot([0, df['ABI MS Base'].max()], [0, df['ABI MS Base'].max()], 'r--', alpha=0.8)
        plt.title('Promo MS vs Base MS')
        plt.xlabel('Base Market Share')
        plt.ylabel('Promo Market Share')
        top_idx = df['ABI MS Promo'].nlargest(3).index
        for idx in top_idx:
            plt.annotate(f"{df['ABI MS Promo'][idx]:.2f}", 
                         (df['ABI MS Base'][idx], df['ABI MS Promo'][idx]),
                         textcoords="offset points", xytext=(5,5), ha='left', fontsize=8, color='blue')
        plt.tight_layout()
        plt.show()
    # 3. Competitive Overlap Analysis (remove average line/label, keep data labels)
    overlap_cols = ['Overlapping', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    overlap_data = []
    overlap_labels = []
    for col in overlap_cols:
        if col in df.columns:
            overlap_data.append(df[col].sum())
            overlap_labels.append(col)
    if overlap_data:
        plt.figure(figsize=(8, 5))
        bars = plt.bar(range(len(overlap_data)), overlap_data)
        plt.title('Competitive Overlap Analysis')
        plt.xticks(range(len(overlap_labels)), overlap_labels, rotation=45, ha='right')
        plt.ylabel('Number of Promotions')
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2, height, int(height), 
                     ha='center', va='bottom', fontsize=9)
        plt.tight_layout()
        plt.show()
    # 4. Seasonal Pattern (with data labels, only once)
    if 'Start_Month' in df.columns:
        monthly_counts = df['Start_Month'].value_counts().sort_index()
        plt.figure(figsize=(8, 5))
        plt.plot(monthly_counts.index, monthly_counts.values, marker='o')
        plt.title('Seasonal Promotion Pattern')
        plt.xlabel('Month')
        plt.ylabel('Number of Promotions')
        plt.xticks(range(1, 13))
        for x, y in zip(monthly_counts.index, monthly_counts.values):
            plt.text(x, y, int(y), ha='center', va='bottom', fontsize=9)
        plt.tight_layout()
        plt.show()
    # 5. Retailer Performance (with data labels, only once)
    if 'ABI MS Promo Uplift - rel' in df.columns and len(df['Retailer'].unique()) > 1:
        retailer_performance = df.groupby('Retailer')['ABI MS Promo Uplift - rel'].mean().sort_values(ascending=True)
        plt.figure(figsize=(8, 5))
        bars = plt.barh(range(len(retailer_performance)), retailer_performance.values)
        plt.title('Average Uplift by Retailer')
        plt.yticks(range(len(retailer_performance)), retailer_performance.index)
        plt.xlabel('Average Relative Uplift')
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width, bar.get_y() + bar.get_height()/2, f"{width:.2f}", 
                     ha='left', va='center', fontsize=9)
        plt.tight_layout()
        plt.show()

def advanced_insights(df):
    print("=== ADVANCED BUSINESS INSIGHTS ===")
    if 'ABI MS Promo Uplift - rel' in df.columns:
        print("Best Performing Promotion Mechanics:")
        mechanic_performance = df.groupby('ABI Mechanic')['ABI MS Promo Uplift - rel'].agg(['mean', 'count']).sort_values('mean', ascending=False)
        print(mechanic_performance)
        print("\nImpact of Competitive Overlap on Performance:")
        if 'Overlapping' in df.columns:
            overlap_impact = df.groupby('Overlapping')['ABI MS Promo Uplift - rel'].agg(['mean', 'count'])
            print("Overlapping vs Non-overlapping:")
            print(overlap_impact)
        print("\nOptimal Promotion Duration Analysis:")
        df['Duration_Category'] = pd.cut(df['ABI_Duration_Days'], 
                                       bins=[0, 3, 7, 14, 30, 100], 
                                       labels=['1-3 days', '4-7 days', '8-14 days', '15-30 days', '30+ days'])
        duration_performance = df.groupby('Duration_Category')['ABI MS Promo Uplift - rel'].agg(['mean', 'count'])
        print(duration_performance)
    print("\n" + "="*50 + "\n")

def competitor_overlap_summary(df, top_n=5):
    print("=== TOP COMPETITOR SKU OVERLAPS FOR EACH ABI SKU ===")
    if 'ABI SKU' not in df.columns or 'Competitor SKU' not in df.columns or 'Overlapping' not in df.columns:
        print("Required columns not found in DataFrame.")
        return None
    overlap_df = df[df['Overlapping'] == 1]
    overlap_counts = (
        overlap_df.groupby(['ABI SKU', 'Competitor SKU'])
        .size()
        .reset_index(name='Num_Overlaps')
    )
    top_competitors = (
        overlap_counts.sort_values(['ABI SKU', 'Num_Overlaps'], ascending=[True, False])
        .groupby('ABI SKU')
        .head(top_n)
        .reset_index(drop=True)
    )
    print(top_competitors)
    print("\n" + "="*50 + "\n")
    return top_competitors

def generate_recommendations(df):
    print("=== STRATEGIC RECOMMENDATIONS ===")
    recommendations = []
    if 'ABI MS Promo Uplift - rel' in df.columns:
        avg_uplift = df['ABI MS Promo Uplift - rel'].mean()
        best_mechanic = df.groupby('ABI Mechanic')['ABI MS Promo Uplift - rel'].mean().idxmax()
        recommendations.append(f"Focus on '{best_mechanic}' promotions - highest average uplift")
        if 'Duration_Category' in df.columns:
            best_duration = df.groupby('Duration_Category')['ABI MS Promo Uplift - rel'].mean().idxmax()
            recommendations.append(f"Optimal promotion duration appears to be: {best_duration}")
        if 'Overlapping' in df.columns:
            overlap_performance = df.groupby('Overlapping')['ABI MS Promo Uplift - rel'].mean()
            if len(overlap_performance) > 1:
                if overlap_performance[0] > overlap_performance[1]:
                    recommendations.append("Non-overlapping promotions perform better - avoid competitor timing")
                else:
                    recommendations.append("Overlapping promotions can be effective - consider competitive response")
        best_retailer = df.groupby('Retailer')['ABI MS Promo Uplift - rel'].mean().idxmax()
        recommendations.append(f"Strongest performance at: {best_retailer}")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    print("\n" + "="*50 + "\n")

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def correlation_matrix_concise(df, threshold=0.8):
    numeric_cols = df.select_dtypes(include=[np.number])
    corr = numeric_cols.corr()

    # Increase figure size for clarity
    plt.figure(figsize=(16, 12))
    
    # Plot heatmap with smaller annotation size and 1 decimal place
    sns.heatmap(corr, annot=True, cmap='coolwarm', fmt='.1f',
                annot_kws={"size": 8}, cbar_kws={"shrink": 0.8})
    
    plt.title("Correlation Matrix", fontsize=16)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    
    # Display highly correlated pairs
    print("\n--- HIGH CORRELATION PAIRS (Potential Multicollinearity) ---")
    for i in range(len(corr.columns)):
        for j in range(i):
            if abs(corr.iloc[i, j]) > threshold:
                print(f"{corr.columns[i]} and {corr.columns[j]}: {corr.iloc[i, j]:.2f}")


file_path = r'demelted_data.csv'
ads_df = load_and_clean_data(file_path)


# Clean up any additional unwanted columns  
ads_df = ads_df.drop(columns=['base_ms_weeks_count'], errors='ignore')

# # REMOVING OUTLIERS
# def remove_extreme_uplift_outliers(df, outlier_threshold=26.26):

#     print("UPLIFT OUTLIER REMOVAL")
#     df_work = df.copy()
    
#     # basic info for my sanity check
#     print(f"Input dataset: {len(df_work)} records")
#     print(f"Columns in dataset: {list(df_work.columns)}")

#     uplift_column = 'ABI MS Promo Uplift - rel'
    
#     retailer_column = 'Retailer'
#     original_count = len(df_work)
    
#     # Convert uplift to numeric if it's not already
#     df_work[uplift_column] = pd.to_numeric(df_work[uplift_column], errors='coerce')
    
#     # Identify extreme outliers
#     print(f"\n Identifying outliers with {uplift_column} > {outlier_threshold}%...")
    
#     extreme_outliers = df_work[df_work[uplift_column] > outlier_threshold].copy()
    
#     if len(extreme_outliers) == 0:
#         print(f"No outliers found with uplift > {outlier_threshold}%")
#         return df_work, pd.DataFrame()
    
#     print(f"Found {len(extreme_outliers)} extreme outliers")
    
#     # Focus specifically on AUCHAN outliers
#     auchan_outliers = extreme_outliers[
#         extreme_outliers[retailer_column].str.contains('AUCHAN', case=False, na=False)
#     ].copy()
    
#     print(f"Found {len(auchan_outliers)} AUCHAN outliers specifically")
    
#     # Display details of outliers being removed
#     if len(auchan_outliers) > 0:
#         print(f"\n--- EXTREME OUTLIER ANALYSIS (Uplift > {outlier_threshold}%) ---")
#         print(f"Found {len(auchan_outliers)} outlier promotions for AUCHAN.")
#         print("\nDetails of Outlier Promotions:")
        
#         relevant_cols = [retailer_column, uplift_column]
        
#         # Add other columns if they exist
#         optional_cols = [
#             'ABI SKU', 'ABI_SKU', 'SKU', 'sku',
#             'ABI Start', 'ABI_Start', 'start_date', 'Start_Date',
#             'ABI Mechanic', 'ABI_Mechanic', 'mechanic', 'Mechanic',
#             'ABI Depth', 'ABI_Depth', 'depth', 'Depth',
#             'ABI MS Promo', 'ABI_MS_Promo', 'ms_promo',
#             'ABI MS Promo Uplift - abs', 'ABI_MS_Promo_Uplift_abs',
#             'Avg Temp', 'Avg_Temp', 'temperature'
#         ]
        
#         for col in optional_cols:
#             if col in df_work.columns:
#                 relevant_cols.append(col)
        
#         # Remove duplicates while preserving order
#         relevant_cols = list(dict.fromkeys(relevant_cols))
        
#         # Display outlier details
#         display_cols = [col for col in relevant_cols if col in auchan_outliers.columns][:8]  # Limit to 8 cols for readability
        
#         for idx, (_, row) in enumerate(auchan_outliers.iterrows(), 1):
#             print(f"Outlier {idx}:")
#             for col in display_cols:
#                 value = row[col]
#                 if pd.isna(value):
#                     value = "N/A"
#                 elif isinstance(value, float):
#                     if col == uplift_column:
#                         value = f"{value:.2f}%"
#                     else:
#                         value = f"{value:.2f}"
#                 print(f"  {col}: {value}")
#             print()
    
#     # Remove the outliers from the dataset
#     print(f"Removing {len(auchan_outliers)} AUCHAN outliers from dataset...")
    
#     # Create a cleaned dataset by removing the outliers
#     cleaned_df = df_work.drop(auchan_outliers.index).copy()
    
#     print(f"Cleaned dataset: {len(cleaned_df)} records (removed {len(auchan_outliers)} outliers)")
    
#     # Summary statistics
#     print(f"\nSUMMARY:")
#     print(f"Original dataset: {original_count} records")
#     print(f"After removing missing uplift: {len(df_work)} records")
#     print(f"Extreme outliers found: {len(extreme_outliers)} records")
#     print(f"AUCHAN outliers removed: {len(auchan_outliers)} records")
#     print(f"Final cleaned dataset: {len(cleaned_df)} records")
    
#     if len(df_work) > 0:
#         print(f"\nUplift statistics (before cleaning):")
#         print(f"  Mean: {df_work[uplift_column].mean():.2f}%")
#         print(f"  Median: {df_work[uplift_column].median():.2f}%")
#         print(f"  Max: {df_work[uplift_column].max():.2f}%")
        
#         if len(cleaned_df) > 0:
#             print(f"\nUplift statistics (after cleaning):")
#             print(f"  Mean: {cleaned_df[uplift_column].mean():.2f}%")
#             print(f"  Median: {cleaned_df[uplift_column].median():.2f}%")
#             print(f"  Max: {cleaned_df[uplift_column].max():.2f}%")
    
#     return cleaned_df, auchan_outliers

# # Run the outlier removal on the loaded dataset
# print("Running outlier removal on the current dataset...")
# df_cleaned, outliers_removed = remove_extreme_uplift_outliers(ads_df, outlier_threshold=26.26)

# # Update the main dataframe with cleaned data
# df_cleaned.drop(columns=['Retailer'], inplace=True)
# ads_df = df_cleaned.copy()

# print(f"\nOutlier removal complete! Dataset updated with {len(ads_df)} clean records.")


df = ads_df
# df.to_csv('clean_new_test_output_ads_v3.csv')

# Run all analysis functions
correlation_matrix_concise(df)
basic_statistics(df)
missing_value_analysis(df)
detect_outliers(df)
promotion_analysis(df)
competitive_analysis(df)
market_share_analysis(df)
retailer_analysis(df)
seasonal_analysis(df)
correlation_analysis(df)
advanced_insights(df)
competitor_overlap_summary(df)
generate_recommendations(df)
create_visualizations(df)
plot_competitor_overlap_matrix(df)
print('Analysis Complete!')

df.columns

def plot_seasonality_for_top_combos(promo_df):
    
    #Show top 5 promo combos and their seasonality for the most relevant SKU
    
    promo_df['Promo_Combo'] = promo_df['ABI Mechanic'] + ' (' + promo_df['ABI Depth'].astype(str) + ')'
    top_5_combos = promo_df['Promo_Combo'].value_counts().nlargest(5).index

    print("=== Top 5 Promo Combinations (by frequency) ===")
    print(promo_df['Promo_Combo'].value_counts().head(5))
    print("\n" + "="*50 + "\n")

    for combo in top_5_combos:
        combo_df = promo_df[promo_df['Promo_Combo'] == combo]
        if combo_df.empty:
            continue

        top_sku = combo_df['ABI SKU'].value_counts().idxmax()
        sku_df = combo_df[combo_df['ABI SKU'] == top_sku]
        
       
        monthly_performance = sku_df.groupby('Start_Month')['ABI MS Promo Uplift - rel'].mean().reset_index()
        
        
        all_months = pd.DataFrame({'Start_Month': range(1, 13)})
        monthly_performance = pd.merge(all_months, monthly_performance, on='Start_Month', how='left')
        
        plt.figure(figsize=(11, 6))
        sns.lineplot(data=monthly_performance, x='Start_Month', y='ABI MS Promo Uplift - rel', marker='o', ci=None, markersize=8)
        
        plt.title(f"Monthly Uplift Seasonality for '{top_sku}'\nwith Promo: '{combo}'", fontsize=14)
        plt.xlabel("Month", fontsize=12)
        plt.ylabel("Average Relative Uplift", fontsize=12)
        plt.xticks(ticks=range(1, 13), labels=['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])
        plt.grid(True, which='both', linestyle='--', linewidth=0.5)

        for index, row in monthly_performance.iterrows():
            if pd.notnull(row['ABI MS Promo Uplift - rel']):
                plt.text(row['Start_Month'], row['ABI MS Promo Uplift - rel'], f"{row['ABI MS Promo Uplift - rel']:.2f}", ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.show()

plot_seasonality_for_top_combos(df.copy())

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


def plot_overlap_seasonality(df):

    # Identifies the top 5 ABI SKUs with the most competitor overlaps and
    # plots their monthly promotion counts vs. overlap counts.

    required_cols = ['Overlapping', 'ABI SKU', 'ABI Start', 'Competitor SKU']
    for col in required_cols:
        if col not in df.columns:
            print(f"Error: DataFrame is missing required column: '{col}'.")
            return

    # Ensure 'ABI Start' is datetime
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])

    # 1. Identify Top 5 SKUs with the most overlaps
    overlap_df = df[df['Overlapping'] == 1]
    if overlap_df.empty:
        print("No overlapping promotions found in the data.")
        return

    top_5_skus = overlap_df['ABI SKU'].value_counts().nlargest(5).index

    print("=== Top 5 SKUs by Number of Competitor Overlaps ===")
    sku_counts = overlap_df['ABI SKU'].value_counts().nlargest(5)
    print(sku_counts)
    print("\nCompetitor SKUs for each top ABI SKU:")
    for sku in top_5_skus:
        comp_skus = overlap_df[overlap_df['ABI SKU'] == sku]['Competitor SKU'].dropna().unique()
        comp_skus_str = ', '.join(comp_skus) if len(comp_skus) > 0 else "None"
        print(f"  - {sku}: {comp_skus_str}")
    print("\n" + "="*50 + "\n")


    for sku in top_5_skus:
        sku_df = df[df['ABI SKU'] == sku].copy()
        sku_df['Month'] = sku_df['ABI Start'].dt.month


        abi_promo_counts = sku_df.groupby('Month').size()

        
        overlap_promo_counts = sku_df[sku_df['Overlapping'] == 1].groupby('Month').size()

        plot_df = pd.DataFrame({
            'ABI Promotions': abi_promo_counts,
            'Overlapping Competitor Promotions': overlap_promo_counts
        })

        plot_df = plot_df.reindex(range(1, 13), fill_value=0).reset_index().rename(columns={'index': 'Month'})


        plot_df_melted = plot_df.melt(id_vars='Month', var_name='Promotion Type', value_name='Count')


        plt.figure(figsize=(12, 7))
        ax = sns.lineplot(
            data=plot_df_melted,
            x='Month',
            y='Count',
            hue='Promotion Type',
            marker='o',
            palette=['#3498db', '#e74c3c']  # ABI: blue, Overlap: red
        )


        for line in ax.lines:
            for x_val, y_val in zip(line.get_xdata(), line.get_ydata()):
                if y_val > 0:
                    ax.text(x_val, y_val, int(y_val),
                            ha='center',
                            va='bottom',
                            fontsize=9,
                            fontweight='bold',
                            color=line.get_color())

        plt.title(f"Monthly Promotion Analysis for SKU: {sku}", fontsize=16, fontweight='bold')
        plt.xlabel("Month", fontsize=12)
        plt.ylabel("Number of Promotions", fontsize=12)
        month_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        plt.xticks(ticks=range(1, 13), labels=month_labels, rotation=45)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        ax.legend(title='Promotion Type', title_fontsize='11', fontsize='10')
        plt.tight_layout()
        plt.show()

plot_overlap_seasonality(df.copy())

def analyze_overlap_impact(promo_df):

    # Analyzes and visualizes the impact of competitive overlap on market share uplift.

    if 'Overlapping' not in promo_df.columns or 'ABI MS Promo Uplift - rel' not in promo_df.columns:
        print("Required columns ('Overlapping', 'ABI MS Promo Uplift - rel') not found.")
        return


    overlap_impact = promo_df.groupby('Overlapping')['ABI MS Promo Uplift - rel'].mean().reset_index()
    overlap_impact['Overlapping'] = overlap_impact['Overlapping'].map({1: 'Yes', 0: 'No'})

    print("=== Average Market Share Uplift: Overlapping vs. Non-Overlapping ===")
    print(overlap_impact)
    

    plt.figure(figsize=(8, 6))
    bars = sns.barplot(data=overlap_impact, x='Overlapping', y='ABI MS Promo Uplift - rel', palette=['salmon', 'skyblue'])
    
    plt.title('Impact of Competitive Overlap on ABI Market Share', fontsize=14)
    plt.xlabel('Overlapping with Competitor?', fontsize=12)
    plt.ylabel('Average Relative Market Share Uplift', fontsize=12)
    
   
    for bar in bars.patches:
        value = bar.get_height() / 10
        plt.text(bar.get_x() + bar.get_width() / 2, 
                 bar.get_height(), 
                 f'{value:.2%}', 
                 ha='center', 
                 va='bottom' if bar.get_height() > 0 else 'top',
                 fontsize=11)

    plt.tight_layout()
    plt.show()


analyze_overlap_impact(df)


def remove_extreme_uplift_outliers(df, outlier_threshold=26.26):

    print("UPLIFT OUTLIER REMOVAL")
    df_work = df.copy()
    
    # basic info for my sanity check
    print(f"Input dataset: {len(df_work)} records")
    print(f"Columns in dataset: {list(df_work.columns)}")
    
    # Try to identify the uplift column (common variations)
    # uplift_column = None
    # possible_uplift_cols = [
    #     'ABI MS Promo Uplift - rel',
    #     'ABI_MS_Promo_Uplift_rel', 
    #     'uplift_rel',
    #     'relative_uplift',
    #     'promo_uplift_rel',
    #     'MS_Promo_Uplift_rel'
    # ]
    
    # for col in possible_uplift_cols:
    #     if col in df_work.columns:
    #         uplift_column = col
    #         break
    
    # if uplift_column is None:
    #     print("Could not find uplift column. Please check column names.")
    #     print("Expected one of:", possible_uplift_cols)
    #     return df_work, pd.DataFrame()
    
    # print(f"Using uplift column: '{uplift_column}'")

    uplift_column = 'ABI MS Promo Uplift - rel'
    
    # # Try to identify retailer column
    # retailer_column = None
    # possible_retailer_cols = ['Retailer', 'retailer', 'RETAILER', 'Enseigne']
    
    # for col in possible_retailer_cols:
    #     if col in df_work.columns:
    #         retailer_column = col
    #         break
    
    # if retailer_column is None:
    #     print("❌ Could not find retailer column. Please check column names.")
    #     return df_work, pd.DataFrame()
    
    # print(f"✅ Using retailer column: '{retailer_column}'")
        # Try to identify retailer column
    retailer_column = 'Retailer'
    original_count = len(df_work)
    
    # Convert uplift to numeric if it's not already
    df_work[uplift_column] = pd.to_numeric(df_work[uplift_column], errors='coerce')
    
    # Identify extreme outliers
    print(f"\n Identifying outliers with {uplift_column} > {outlier_threshold}%...")
    
    extreme_outliers = df_work[df_work[uplift_column] > outlier_threshold].copy()
    
    if len(extreme_outliers) == 0:
        print(f"No outliers found with uplift > {outlier_threshold}%")
        return df_work, pd.DataFrame()
    
    print(f"Found {len(extreme_outliers)} extreme outliers")
    
    # Focus specifically on AUCHAN outliers
    auchan_outliers = extreme_outliers[
        extreme_outliers[retailer_column].str.contains('AUCHAN', case=False, na=False)
    ].copy()
    
    print(f"Found {len(auchan_outliers)} AUCHAN outliers specifically")
    
    # Display details of outliers being removed
    if len(auchan_outliers) > 0:
        print(f"\n--- EXTREME OUTLIER ANALYSIS (Uplift > {outlier_threshold}%) ---")
        print(f"Found {len(auchan_outliers)} outlier promotions for AUCHAN.")
        print("\nDetails of Outlier Promotions:")
        
        relevant_cols = [retailer_column, uplift_column]
        
        # Add other columns if they exist
        optional_cols = [
            'ABI SKU', 'ABI_SKU', 'SKU', 'sku',
            'ABI Start', 'ABI_Start', 'start_date', 'Start_Date',
            'ABI Mechanic', 'ABI_Mechanic', 'mechanic', 'Mechanic',
            'ABI Depth', 'ABI_Depth', 'depth', 'Depth',
            'ABI MS Promo', 'ABI_MS_Promo', 'ms_promo',
            'ABI MS Promo Uplift - abs', 'ABI_MS_Promo_Uplift_abs',
            'Avg Temp', 'Avg_Temp', 'temperature'
        ]
        
        for col in optional_cols:
            if col in df_work.columns:
                relevant_cols.append(col)
        
        # Remove duplicates while preserving order
        relevant_cols = list(dict.fromkeys(relevant_cols))
        
        # Display outlier details
        display_cols = [col for col in relevant_cols if col in auchan_outliers.columns][:8]  # Limit to 8 cols for readability
        
        for idx, (_, row) in enumerate(auchan_outliers.iterrows(), 1):
            print(f"Outlier {idx}:")
            for col in display_cols:
                value = row[col]
                if pd.isna(value):
                    value = "N/A"
                elif isinstance(value, float):
                    if col == uplift_column:
                        value = f"{value:.2f}%"
                    else:
                        value = f"{value:.2f}"
                print(f"  {col}: {value}")
            print()
    
    # Remove the outliers from the dataset
    print(f"Removing {len(auchan_outliers)} AUCHAN outliers from dataset...")
    
    # Create a cleaned dataset by removing the outliers
    cleaned_df = df_work.drop(auchan_outliers.index).copy()
    
    print(f"Cleaned dataset: {len(cleaned_df)} records (removed {len(auchan_outliers)} outliers)")
    
    # Summary statistics
    print(f"\nSUMMARY:")
    print(f"Original dataset: {original_count} records")
    print(f"After removing missing uplift: {len(df_work)} records")
    print(f"Extreme outliers found: {len(extreme_outliers)} records")
    print(f"AUCHAN outliers removed: {len(auchan_outliers)} records")
    print(f"Final cleaned dataset: {len(cleaned_df)} records")
    
    if len(df_work) > 0:
        print(f"\nUplift statistics (before cleaning):")
        print(f"  Mean: {df_work[uplift_column].mean():.2f}%")
        print(f"  Median: {df_work[uplift_column].median():.2f}%")
        print(f"  Max: {df_work[uplift_column].max():.2f}%")
        
        if len(cleaned_df) > 0:
            print(f"\nUplift statistics (after cleaning):")
            print(f"  Mean: {cleaned_df[uplift_column].mean():.2f}%")
            print(f"  Median: {cleaned_df[uplift_column].median():.2f}%")
            print(f"  Max: {cleaned_df[uplift_column].max():.2f}%")
    
    return cleaned_df, auchan_outliers

# Run the outlier removal on the loaded dataset
print("Running outlier removal on the current dataset...")
df_cleaned, outliers_removed = remove_extreme_uplift_outliers(df, outlier_threshold=26.26)

# Update the main dataframe with cleaned data
df = df_cleaned.copy()

print(f"\nOutlier removal complete! Dataset updated with {len(df)} clean records.")


## AFTER REMOVING OUTLIERS

# Run all analysis functions
correlation_matrix_concise(df)
basic_statistics(df)
missing_value_analysis(df)
detect_outliers(df)
promotion_analysis(df)
competitive_analysis(df)
market_share_analysis(df)
retailer_analysis(df)
seasonal_analysis(df)
correlation_analysis(df)
advanced_insights(df)
competitor_overlap_summary(df)
generate_recommendations(df)
create_visualizations(df)
plot_competitor_overlap_matrix(df)
print('Analysis Complete!')


def analyze_overlap_impact(promo_df):

    # Analyzes and visualizes the impact of competitive overlap on market share uplift.

    if 'Overlapping' not in promo_df.columns or 'ABI MS Promo Uplift - rel' not in promo_df.columns:
        print("Required columns ('Overlapping', 'ABI MS Promo Uplift - rel') not found.")
        return


    overlap_impact = promo_df.groupby('Overlapping')['ABI MS Promo Uplift - rel'].mean().reset_index()
    overlap_impact['Overlapping'] = overlap_impact['Overlapping'].map({1: 'Yes', 0: 'No'})

    print("=== Average Market Share Uplift: Overlapping vs. Non-Overlapping ===")
    print(overlap_impact)
    

    plt.figure(figsize=(8, 6))
    bars = sns.barplot(data=overlap_impact, x='Overlapping', y='ABI MS Promo Uplift - rel', palette=['salmon', 'skyblue'])
    
    plt.title('Impact of Competitive Overlap on ABI Market Share', fontsize=14)
    plt.xlabel('Overlapping with Competitor?', fontsize=12)
    plt.ylabel('Average Relative Market Share Uplift', fontsize=12)
    
   
    for bar in bars.patches:
        value = bar.get_height() / 10
        plt.text(bar.get_x() + bar.get_width() / 2, 
                 bar.get_height(), 
                 f'{value:.2%}', 
                 ha='center', 
                 va='bottom' if bar.get_height() > 0 else 'top',
                 fontsize=11)

    plt.tight_layout()
    plt.show()


analyze_overlap_impact(df)
