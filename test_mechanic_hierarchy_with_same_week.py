#!/usr/bin/env python3
"""
Test MODEL with Mechanic as Hierarchy + Same Week included
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.formula.api as smf
from statsmodels.regression.mixed_linear_model import MixedLM
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def main():
    print("="*80)
    print("TEST: MECHANIC AS HIERARCHY + SAME WEEK INCLUDED")
    print("="*80)
    
    # Load and prepare data
    df = pd.read_csv('demelted_data.csv')
    
    # Create variables
    df['Before'] = df['1 wk before'].fillna(0).astype(int)
    df['After'] = df['1 wk after'].fillna(0).astype(int)
    df['Same_Week'] = df['Same Week'].fillna(0).astype(int)  # ADD BACK Same Week
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    
    # Create standardized variables
    scaler = StandardScaler()
    df['ABI_Duration_Days'] = (pd.to_datetime(df['ABI End']) - pd.to_datetime(df['ABI Start'])).dt.days
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    
    for var in ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    # Extract brand and clean mechanic
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    df['ABI_Mechanic'] = df['ABI Mechanic']
    df['KSM'] = df['KSM'].astype(int)
    
    # Prepare model data
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                 'Before', 'After', 'Same_Week', 'Avg_Temp_std', 'ABI_vs_Segment_PTC_Index_Agg_std', 
                 'Retailer', 'Brand', 'ABI_Mechanic', 'KSM']
    
    df_model = df[model_vars].dropna()
    print(f"Model data shape: {df_model.shape}")
    
    # Check data distribution
    print(f"\nData distribution:")
    print(f"Retailers: {df_model['Retailer'].nunique()} unique")
    print(f"Brands: {df_model['Brand'].nunique()} unique") 
    print(f"Mechanics: {df_model['ABI_Mechanic'].nunique()} unique")
    print(f"Mechanic distribution: {df_model['ABI_Mechanic'].value_counts()}")
    
    # Check timing variables
    print(f"\nTiming variables:")
    print(f"Before: {df_model['Before'].value_counts().sort_index()}")
    print(f"After: {df_model['After'].value_counts().sort_index()}")
    print(f"Same_Week: {df_model['Same_Week'].value_counts().sort_index()}")
    
    # Base formula WITH Same Week and mechanic as hierarchy
    base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std + 
                     Before + After + Same_Week + Avg_Temp_std + ABI_vs_Segment_PTC_Index_Agg_std + KSM"""
    
    print(f"\nBase formula: {base_formula}")
    
    try:
        print("\n" + "-"*60)
        print("FITTING: BRAND + RETAILER + MECHANIC HIERARCHY + SAME WEEK")
        print("-"*60)
        
        # Try crossed random effects with mechanic as additional grouping
        model_full = MixedLM.from_formula(
            base_formula, 
            df_model,
            groups=df_model["Retailer"],  # Primary grouping by retailer
            re_formula="1",               # Random intercept for retailer
            vc_formula={
                "Brand": "0 + C(Brand)",           # Variance component for brand
                "Mechanic": "0 + C(ABI_Mechanic)" # Variance component for mechanic
            }
        ).fit()
        
        print("✅ MODEL FITTED SUCCESSFULLY!")
        print("\nModel Summary:")
        print(model_full.summary())
        
        print(f"\n" + "="*60)
        print("KEY TIMING COEFFICIENTS")
        print("="*60)
        
        before_coeff = model_full.params['Before']
        after_coeff = model_full.params['After']
        same_week_coeff = model_full.params['Same_Week']
        before_p = model_full.pvalues['Before']
        after_p = model_full.pvalues['After']
        same_week_p = model_full.pvalues['Same_Week']
        
        print(f"Before coefficient: {before_coeff:.4f} (p={before_p:.4f})")
        print(f"After coefficient: {after_coeff:.4f} (p={after_p:.4f})")
        print(f"Same Week coefficient: {same_week_coeff:.4f} (p={same_week_p:.4f})")
        print(f"Before > After: {before_coeff > after_coeff}")
        
        print(f"\n" + "="*60)
        print("VARIANCE DECOMPOSITION")
        print("="*60)
        
        retailer_var = model_full.cov_re.iloc[0,0]
        print(f"Retailer variance: {retailer_var:.4f}")
        
        if hasattr(model_full, 'vcomp') and len(model_full.vcomp) > 0:
            print(f"Variance components:")
            for i, comp in enumerate(model_full.vcomp):
                comp_name = "Brand" if i == 0 else "Mechanic" if i == 1 else f"Component_{i}"
                print(f"  {comp_name}: {comp:.4f}")
        
        residual_var = model_full.scale
        print(f"Residual variance: {residual_var:.4f}")
        
        # Calculate total variance and ICCs
        total_var = retailer_var + residual_var
        if hasattr(model_full, 'vcomp'):
            total_var += sum(model_full.vcomp)
        
        retailer_icc = retailer_var / total_var
        print(f"\nRetailer ICC: {retailer_icc:.4f} ({retailer_icc*100:.1f}%)")
        
        if hasattr(model_full, 'vcomp') and len(model_full.vcomp) >= 2:
            brand_icc = model_full.vcomp[0] / total_var
            mechanic_icc = model_full.vcomp[1] / total_var
            print(f"Brand ICC: {brand_icc:.4f} ({brand_icc*100:.1f}%)")
            print(f"Mechanic ICC: {mechanic_icc:.4f} ({mechanic_icc*100:.1f}%)")
        
        print(f"\n" + "="*60)
        print("CONCLUSION")
        print("="*60)
        
        if before_coeff > after_coeff:
            print(f"✅ SUCCESS: Before > After even with Same Week and Mechanic hierarchy!")
            print(f"   This suggests the timing effect is robust")
        else:
            print(f"❌ ISSUE: After > Before when Same Week and Mechanic are included")
            print(f"   Same Week or Mechanic hierarchy may be suppressing Before effect")
        
        print(f"\nSame Week effect: {same_week_coeff:.4f}")
        if same_week_coeff > max(before_coeff, after_coeff):
            print(f"⚠️  Same Week dominates timing effects - potential suppression")
        
    except Exception as e:
        print(f"❌ ERROR FITTING MODEL: {e}")
        
        # Try simpler alternative
        print(f"\nTrying simpler alternative...")
        try:
            # Just add mechanic as fixed effect
            formula_alt = base_formula + " + C(ABI_Mechanic)"
            model_alt = MixedLM.from_formula(
                formula_alt,
                df_model,
                groups=df_model["Retailer"]
            ).fit()
            
            print("✅ Alternative model fitted!")
            print(f"Before: {model_alt.params['Before']:.4f} (p={model_alt.pvalues['Before']:.4f})")
            print(f"After: {model_alt.params['After']:.4f} (p={model_alt.pvalues['After']:.4f})")
            print(f"Same Week: {model_alt.params['Same_Week']:.4f} (p={model_alt.pvalues['Same_Week']:.4f})")
            print(f"Before > After: {model_alt.params['Before'] > model_alt.params['After']}")
            
        except Exception as e2:
            print(f"❌ Alternative also failed: {e2}")

if __name__ == "__main__":
    main()
