import pandas as pd
import numpy as np
from statsmodels.stats.outliers_influence import variance_inflation_factor
from sklearn.preprocessing import LabelEncoder
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')


file_path = 'demelted_data.csv'
df = pd.read_csv(file_path)
df.columns = df.columns.str.strip()


# Rename columns for consistency
col_map = {
    'ABI Coverage': 'ABI_Coverage',
    'ABI Mechanic': 'ABI_Mechanic',
    'Overlapping': 'Overlapping',
    'Same Week': 'Same_Week',
    '1 wk after': '1wk_after',
    '2 wk after': '2wk_after',
    '1 wk before': '1wk_before',
    '2 wk before': '2wk_before',
    'Avg Temp': 'Avg_Temp',
    'ABI vs Segment PTC Index Agg': 'ABI_vs_Segment_PTC_Index_Agg'
}
df = df.rename(columns=col_map)
# Combine 'after' and 'before' columns
# df['After'] = df[['1wk_after', '2wk_after']].apply(pd.to_numeric, errors='coerce').sum(axis=1)
# df['Before'] = df[['1wk_before', '2wk_before']].apply(pd.to_numeric, errors='coerce').sum(axis=1)


# Select relevant columns
cols = [
    'ABI_Mechanic',
    'Overlapping',
    'Same_Week',
    # 'After',
    # 'Before',
    '1wk_after',
    '2wk_after',
    '1wk_before',
    '2wk_before',
    'Avg_Temp',
    'ABI_vs_Segment_PTC_Index_Agg',
    'ABI_Coverage'
]
df_vif = df[cols].copy()


# Encode categorical variables
for col in ['ABI_Mechanic', 'Same_Week']:
    if df_vif[col].dtype == 'object':
        le = LabelEncoder()
        df_vif[col] = le.fit_transform(df_vif[col].astype(str))


# Ensure all columns are numeric and clean data
for col in df_vif.columns:
    df_vif[col] = pd.to_numeric(df_vif[col], errors='coerce')

# Drop rows with any NaN values
df_vif = df_vif.dropna()

# Check data types and shape
print(f"Data shape: {df_vif.shape}")
print(f"Data types:\n{df_vif.dtypes}")
print(f"First few rows:\n{df_vif.head()}")

# Ensure we have enough data points
if df_vif.shape[0] < df_vif.shape[1]:
    print(f"Warning: Not enough observations ({df_vif.shape[0]}) for {df_vif.shape[1]} variables")


# Add constant term for VIF calculation
from statsmodels.tools import add_constant

# Convert to numpy array and ensure it's float64
X = df_vif.values.astype(np.float64)

# Add constant (intercept) term
X_with_const = add_constant(X)

# Calculate VIF for each feature (excluding the constant)
vif_data = pd.DataFrame()
vif_data['feature'] = df_vif.columns
vif_data['VIF'] = [variance_inflation_factor(X_with_const, i+1) for i in range(len(df_vif.columns))]
vif_data = vif_data.sort_values('VIF', ascending=False)

print(f"\nVIF Results:")
print("=" * 50)
vif_data

# Detailed multicollinearity analysis
print("Multicollinearity Assessment:")
print("=" * 50)
print("VIF Interpretation:")
print("- VIF = 1: No multicollinearity")
print("- 1 < VIF < 5: Moderate multicollinearity")
print("- 5 <= VIF < 10: High multicollinearity")
print("- VIF >= 10: Very high multicollinearity (problematic)")
print()

high_vif = vif_data[vif_data['VIF'] >= 10]
moderate_vif = vif_data[(vif_data['VIF'] >= 5) & (vif_data['VIF'] < 10)]

if len(high_vif) > 0:
    print("Features with Very High Multicollinearity (VIF >= 10):")
    for _, row in high_vif.iterrows():
        print(f"  - {row['feature']}: VIF = {row['VIF']:.2f}")
    print()

if len(moderate_vif) > 0:
    print("Features with High Multicollinearity (5 <= VIF < 10):")
    for _, row in moderate_vif.iterrows():
        print(f"  - {row['feature']}: VIF = {row['VIF']:.2f}")
    print()

print("All Features VIF Summary:")
for _, row in vif_data.iterrows():
    status = "PROBLEMATIC" if row['VIF'] >= 10 else "HIGH" if row['VIF'] >= 5 else "MODERATE" if row['VIF'] > 1 else "GOOD"
    print(f"  {row['feature']:<30} VIF: {row['VIF']:>8.2f} ({status})")


# Linear Regression Analysis
Linear regression model with ABI_vs_Segment_PTC_Index_Agg as target variable and feature importance analysis.


# Prepare data for Linear Regression
target_col = 'ABI_vs_Segment_PTC_Index_Agg'
feature_cols = [
    'ABI_Mechanic',
    'Overlapping', 
    'Same_Week',
    'After',
    'Before',
    'Avg_Temp',
    'ABI_Coverage'
]

# Check if target column exists in the original dataframe
if target_col not in df.columns:
    print(f"Warning: {target_col} not found in data. Available columns:")
    print([col for col in df.columns if 'PTC' in col or 'Index' in col])
else:
    print(f"Target variable: {target_col}")
    print(f"Features: {feature_cols}")
    
# Create regression dataset
lr_data = df[feature_cols + [target_col]].copy()

# Encode categorical variables for regression
for col in ['ABI_Mechanic', 'Same_Week']:
    if col in lr_data.columns and lr_data[col].dtype == 'object':
        le = LabelEncoder()
        lr_data[col] = le.fit_transform(lr_data[col].astype(str))

# Ensure all columns are numeric
for col in lr_data.columns:
    lr_data[col] = pd.to_numeric(lr_data[col], errors='coerce')

# Drop rows with missing values
lr_data = lr_data.dropna()

print(f"\nRegression dataset shape: {lr_data.shape}")
print(f"Target variable stats:\n{lr_data[target_col].describe()}")


# Split data and train Linear Regression model
X = lr_data[feature_cols]
y = lr_data[target_col]

# Split into train and test sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train Linear Regression model
lr_model = LinearRegression()
lr_model.fit(X_train, y_train)

# Make predictions
y_pred_train = lr_model.predict(X_train)
y_pred_test = lr_model.predict(X_test)

# Calculate metrics
train_r2 = r2_score(y_train, y_pred_train)
test_r2 = r2_score(y_test, y_pred_test)
train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
train_mae = mean_absolute_error(y_train, y_pred_train)
test_mae = mean_absolute_error(y_test, y_pred_test)

print("Linear Regression Model Performance:")
print("=" * 50)
print(f"Training R² Score: {train_r2:.4f}")
print(f"Test R² Score: {test_r2:.4f}")
print(f"Training RMSE: {train_rmse:.4f}")
print(f"Test RMSE: {test_rmse:.4f}")
print(f"Training MAE: {train_mae:.4f}")
print(f"Test MAE: {test_mae:.4f}")


# Feature Importance Analysis
coefficients = lr_model.coef_
intercept = lr_model.intercept_

# Create feature importance dataframe
importance_df = pd.DataFrame({
    'Feature': feature_cols,
    'Coefficient': coefficients,
    'Abs_Coefficient': np.abs(coefficients)
}).sort_values('Abs_Coefficient', ascending=False)

print("Feature Importance (Linear Regression Coefficients):")
print("=" * 60)
print(f"Intercept: {intercept:.4f}")
print()
print("Features ranked by absolute coefficient magnitude:")
for _, row in importance_df.iterrows():
    direction = "positive" if row['Coefficient'] > 0 else "negative"
    print(f"{row['Feature']:<20} Coef: {row['Coefficient']:>8.4f} ({direction} impact)")

# Calculate standardized coefficients for better comparison
from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
lr_scaled = LinearRegression()
lr_scaled.fit(X_scaled, y)

std_importance_df = pd.DataFrame({
    'Feature': feature_cols,
    'Std_Coefficient': lr_scaled.coef_,
    'Abs_Std_Coefficient': np.abs(lr_scaled.coef_)
}).sort_values('Abs_Std_Coefficient', ascending=False)

print("\nStandardized Feature Importance (for fair comparison):")
print("=" * 60)
for _, row in std_importance_df.iterrows():
    direction = "positive" if row['Std_Coefficient'] > 0 else "negative"
    print(f"{row['Feature']:<20} Std Coef: {row['Std_Coefficient']:>8.4f} ({direction})")


# Visualization: Linear Regression Plots
plt.style.use('default')
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('Linear Regression Analysis: ABI vs Segment PTC Index', fontsize=16, fontweight='bold')

# Plot 1: Actual vs Predicted (Test Set)
axes[0, 0].scatter(y_test, y_pred_test, alpha=0.6, color='blue', s=50)
axes[0, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
axes[0, 0].set_xlabel('Actual Values')
axes[0, 0].set_ylabel('Predicted Values')
axes[0, 0].set_title(f'Actual vs Predicted (Test Set)\nR² = {test_r2:.3f}')
axes[0, 0].grid(True, alpha=0.3)

# Plot 2: Residuals vs Predicted
residuals = y_test - y_pred_test
axes[0, 1].scatter(y_pred_test, residuals, alpha=0.6, color='green', s=50)
axes[0, 1].axhline(y=0, color='r', linestyle='--', lw=2)
axes[0, 1].set_xlabel('Predicted Values')
axes[0, 1].set_ylabel('Residuals')
axes[0, 1].set_title('Residuals vs Predicted')
axes[0, 1].grid(True, alpha=0.3)

# Plot 3: Feature Importance (Raw Coefficients)
bars1 = axes[1, 0].barh(importance_df['Feature'], importance_df['Coefficient'], 
                       color=['red' if x < 0 else 'blue' for x in importance_df['Coefficient']])
axes[1, 0].set_xlabel('Coefficient Value')
axes[1, 0].set_title('Feature Importance (Raw Coefficients)')
axes[1, 0].grid(True, alpha=0.3, axis='x')

# Plot 4: Feature Importance (Standardized Coefficients)
bars2 = axes[1, 1].barh(std_importance_df['Feature'], std_importance_df['Std_Coefficient'],
                       color=['red' if x < 0 else 'blue' for x in std_importance_df['Std_Coefficient']])
axes[1, 1].set_xlabel('Standardized Coefficient')
axes[1, 1].set_title('Feature Importance (Standardized)')
axes[1, 1].grid(True, alpha=0.3, axis='x')

plt.tight_layout()
plt.show()


# Additional Feature Importance Visualization
fig, axes = plt.subplots(1, 2, figsize=(16, 6))

# Feature importance magnitude comparison
combined_importance = pd.merge(
    importance_df[['Feature', 'Abs_Coefficient']], 
    std_importance_df[['Feature', 'Abs_Std_Coefficient']], 
    on='Feature'
)

# Plot raw vs standardized importance
x_pos = np.arange(len(combined_importance))
width = 0.35

bars1 = axes[0].bar(x_pos - width/2, combined_importance['Abs_Coefficient'], width, 
                   label='Raw Coefficients', alpha=0.8, color='skyblue')
bars2 = axes[0].bar(x_pos + width/2, combined_importance['Abs_Std_Coefficient'], width,
                   label='Standardized Coefficients', alpha=0.8, color='lightcoral')

axes[0].set_xlabel('Features')
axes[0].set_ylabel('Absolute Coefficient Value')
axes[0].set_title('Feature Importance Comparison')
axes[0].set_xticks(x_pos)
axes[0].set_xticklabels(combined_importance['Feature'], rotation=45, ha='right')
axes[0].legend()
axes[0].grid(True, alpha=0.3)

# Correlation heatmap between features and target
corr_data = lr_data[feature_cols + [target_col]].corr()
target_corr = corr_data[target_col].drop(target_col).abs().sort_values(ascending=False)

im = axes[1].imshow([target_corr.values], aspect='auto', cmap='RdYlBu_r', vmin=-1, vmax=1)
axes[1].set_xticks(range(len(target_corr)))
axes[1].set_xticklabels(target_corr.index, rotation=45, ha='right')
axes[1].set_yticks([0])
axes[1].set_yticklabels(['Correlation with Target'])
axes[1].set_title('Feature Correlation with Target Variable')

# Add correlation values on the heatmap
for i, val in enumerate(target_corr.values):
    axes[1].text(i, 0, f'{val:.3f}', ha='center', va='center', fontweight='bold')

plt.tight_layout()
plt.show()


# Summary Report: Linear Regression & Feature Importance
print("="*80)
print("LINEAR REGRESSION ANALYSIS SUMMARY")
print("="*80)

print(f"\nModel Performance:")
print(f"  • R² Score (Test): {test_r2:.4f} ({test_r2*100:.1f}% variance explained)")
print(f"  • RMSE (Test): {test_rmse:.4f}")
print(f"  • MAE (Test): {test_mae:.4f}")

print(f"\nTop 3 Most Important Features (by standardized coefficients):")
for i, (_, row) in enumerate(std_importance_df.head(3).iterrows(), 1):
    impact = "increases" if row['Std_Coefficient'] > 0 else "decreases"
    print(f"  {i}. {row['Feature']}: {impact} target by {abs(row['Std_Coefficient']):.4f} std units")

print(f"\nFeature Impact Direction:")
for _, row in std_importance_df.iterrows():
    direction = "↑ Positive" if row['Std_Coefficient'] > 0 else "↓ Negative"
    magnitude = "High" if abs(row['Std_Coefficient']) > 0.5 else "Medium" if abs(row['Std_Coefficient']) > 0.2 else "Low"
    print(f"  • {row['Feature']:<20} {direction:<12} ({magnitude} impact)")

print(f"\nMulticollinearity vs Feature Importance:")
print(f"  Features with high VIF (>5) that are also important:")
if 'vif_data' in locals():
    high_vif_features = set(vif_data[vif_data['VIF'] > 5]['feature'].tolist())
    important_features = set(std_importance_df.head(3)['Feature'].tolist())
    overlap = high_vif_features.intersection(important_features)
    if overlap:
        for feature in overlap:
            vif_val = vif_data[vif_data['feature'] == feature]['VIF'].iloc[0]
            importance_val = std_importance_df[std_importance_df['Feature'] == feature]['Std_Coefficient'].iloc[0]
            print(f"    - {feature}: VIF={vif_val:.2f}, Importance={importance_val:.4f}")
    else:
        print("    - No overlap between high VIF and top important features")
else:
    print("    - VIF analysis not available for comparison")

print("="*80)
