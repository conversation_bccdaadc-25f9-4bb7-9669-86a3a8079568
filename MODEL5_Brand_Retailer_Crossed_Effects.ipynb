# MODEL 5: Brand + Retailer Crossed Random Effects Analysis

This notebook implements the most sophisticated hierarchical model approach:
**Brand + Retailer Crossed Random Effects**

## Key Features:
- Accounts for both retailer AND brand random effects simultaneously
- Provides the 'cleanest' estimate of timing effects
- Controls for the largest sources of variance in the data
- Gives reliable Before vs After coefficient comparison

## Expected Results:
- Before coefficient > After coefficient
- Brand effects account for ~28% of variance
- Retailer effects account for ~1% of variance
- Marginal significance for timing effects

**Target Variable:** ABI MS Uplift rel
**Key Predictors:** Before (1 wk), After (1 wk)


# Imports and setup
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
import statsmodels.api as sm
import statsmodels.formula.api as smf
from statsmodels.regression.mixed_linear_model import MixedLM
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Libraries loaded successfully")
print("MODEL 5: Brand + Retailer Crossed Random Effects Analysis")
print("=" * 60)


## Data Loading and Preparation


def load_and_prepare_data(data_path):
    """Load and prepare data specifically for MODEL 5 analysis"""
    logger.info("Loading and preparing data for MODEL 5...")

    # Load data
    df_raw = pd.read_csv(data_path)
    logger.info(f"Loaded data with shape: {df_raw.shape}")

    # Start with a copy
    df = df_raw.copy()

    # Handle missing values and infinities
    df.replace([np.inf, -np.inf, ""], np.nan, inplace=True)

    # Remove duplicates
    print(f"Before deduplication: {len(df)} rows")
    df = df.drop_duplicates()
    print(f"After deduplication: {len(df)} rows")

    # Convert date columns
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])

    # Create duration in days
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days

    # Extract brand from ABI SKU
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    
    # Create timing variables (key predictors)
    df['Before'] = df['1 wk before'].fillna(0).astype(int)
    df['After'] = df['1 wk after'].fillna(0).astype(int)

    # Clean and prepare key variables
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')

    # Target variable
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')

    # Remove rows with missing target variable
    df = df.dropna(subset=['ABI_MS_Uplift_Rel'])

    # Handle extreme outliers in target (cap at 95th percentile * 3)
    uplift_95 = df['ABI_MS_Uplift_Rel'].quantile(0.95)
    df['ABI_MS_Uplift_Rel'] = np.where(df['ABI_MS_Uplift_Rel'] > uplift_95 * 3,
                                      uplift_95 * 3, df['ABI_MS_Uplift_Rel'])

    # Convert categorical variables
    df['Retailer'] = df['Retailer'].astype('category')
    df['Brand'] = df['Brand'].astype('category')
    df['KSM'] = df['KSM'].astype(int)

    # Standardize continuous variables for better convergence
    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']
    scaler = StandardScaler()

    for var in continuous_vars:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])

    print("\n" + "="*80)
    print("DATA PREPARATION SUMMARY - MODEL 5")
    print("="*80)
    print(f"Final dataset shape: {df.shape}")
    print(f"Brands: {df['Brand'].value_counts()}")
    print(f"Retailers: {df['Retailer'].value_counts()}")
    print(f"\nTiming Variables:")
    print(f"Before (1 wk): {df['Before'].value_counts().sort_index()}")
    print(f"After (1 wk): {df['After'].value_counts().sort_index()}")
    print(f"\nTarget variable statistics:")
    print(df['ABI_MS_Uplift_Rel'].describe())

    return df


# Load and prepare the data
data_path = "demelted_data.csv"
df_clean = load_and_prepare_data(data_path)


## Exploratory Data Analysis for MODEL 5


def explore_data_for_model5(df):
    """Explore data specifically for MODEL 5 requirements"""
    print("\n" + "="*80)
    print("EXPLORATORY DATA ANALYSIS - MODEL 5 FOCUS")
    print("="*80)
    
    # 1. Check timing effects by brand and retailer
    print("\n1. TIMING EFFECTS BY BRAND")
    print("-" * 40)
    
    for brand in df['Brand'].unique():
        brand_data = df[df['Brand'] == brand]
        
        before_0 = brand_data[brand_data['Before'] == 0]['ABI_MS_Uplift_Rel']
        before_1 = brand_data[brand_data['Before'] == 1]['ABI_MS_Uplift_Rel']
        after_0 = brand_data[brand_data['After'] == 0]['ABI_MS_Uplift_Rel']
        after_1 = brand_data[brand_data['After'] == 1]['ABI_MS_Uplift_Rel']
        
        if len(before_0) > 0 and len(before_1) > 0:
            before_diff = before_1.mean() - before_0.mean()
        else:
            before_diff = np.nan
            
        if len(after_0) > 0 and len(after_1) > 0:
            after_diff = after_1.mean() - after_0.mean()
        else:
            after_diff = np.nan
        
        print(f"{brand}: Before effect = {before_diff:.3f}, After effect = {after_diff:.3f}")
        print(f"  Sample: {len(brand_data)} total, {len(before_1)} before, {len(after_1)} after")
    
    # 2. Check variance components
    print("\n2. VARIANCE ANALYSIS")
    print("-" * 40)
    
    # Overall variance
    total_var = df['ABI_MS_Uplift_Rel'].var()
    print(f"Total variance in uplift: {total_var:.3f}")
    
    # Between-brand variance
    brand_means = df.groupby('Brand')['ABI_MS_Uplift_Rel'].mean()
    between_brand_var = brand_means.var()
    print(f"Between-brand variance: {between_brand_var:.3f} ({between_brand_var/total_var*100:.1f}% of total)")
    
    # Between-retailer variance
    retailer_means = df.groupby('Retailer')['ABI_MS_Uplift_Rel'].mean()
    between_retailer_var = retailer_means.var()
    print(f"Between-retailer variance: {between_retailer_var:.3f} ({between_retailer_var/total_var*100:.1f}% of total)")
    
    # 3. Cross-tabulation of brand x retailer
    print("\n3. BRAND x RETAILER DISTRIBUTION")
    print("-" * 40)
    
    brand_retailer_crosstab = pd.crosstab(df['Brand'], df['Retailer'])
    print(brand_retailer_crosstab)
    
    # Check if we have sufficient observations for crossed effects
    min_obs = brand_retailer_crosstab.min().min()
    print(f"\nMinimum observations per Brand x Retailer cell: {min_obs}")
    
    if min_obs >= 5:
        print("✅ Sufficient data for crossed random effects")
    else:
        print("⚠️  Some Brand x Retailer combinations have few observations")
    
    # 4. Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Brand effects
    df.boxplot(column='ABI_MS_Uplift_Rel', by='Brand', ax=axes[0,0])
    axes[0,0].set_title('Uplift Distribution by Brand')
    axes[0,0].set_xlabel('Brand')
    
    # Retailer effects
    df.boxplot(column='ABI_MS_Uplift_Rel', by='Retailer', ax=axes[0,1])
    axes[0,1].set_title('Uplift Distribution by Retailer')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # Timing effects
    timing_data = []
    for before in [0, 1]:
        for after in [0, 1]:
            subset = df[(df['Before'] == before) & (df['After'] == after)]
            if len(subset) > 0:
                timing_data.extend([(f'B{before}_A{after}', val) for val in subset['ABI_MS_Uplift_Rel']])
    
    timing_df = pd.DataFrame(timing_data, columns=['Timing', 'Uplift'])
    timing_df.boxplot(column='Uplift', by='Timing', ax=axes[1,0])
    axes[1,0].set_title('Uplift by Timing (B=Before, A=After)')
    
    # Brand x Timing interaction
    brand_timing = df.groupby(['Brand', 'Before', 'After'])['ABI_MS_Uplift_Rel'].mean().reset_index()
    brand_timing['Timing'] = 'B' + brand_timing['Before'].astype(str) + '_A' + brand_timing['After'].astype(str)
    
    for brand in df['Brand'].unique():
        brand_subset = brand_timing[brand_timing['Brand'] == brand]
        axes[1,1].plot(brand_subset['Timing'], brand_subset['ABI_MS_Uplift_Rel'], 
                      marker='o', label=brand, linewidth=2)
    
    axes[1,1].set_title('Brand x Timing Interaction')
    axes[1,1].legend()
    axes[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    return brand_retailer_crosstab


# Explore data for MODEL 5
brand_retailer_crosstab = explore_data_for_model5(df_clean)


## MODEL 5: Brand + Retailer Crossed Random Effects Implementation


def build_model5_crossed_effects(df):
    """Build MODEL 5: Brand + Retailer Crossed Random Effects"""
    logger.info("Building MODEL 5: Brand + Retailer Crossed Random Effects...")

    print("\n" + "="*80)
    print("MODEL 5: BRAND + RETAILER CROSSED RANDOM EFFECTS")
    print("="*80)

    # Prepare data for modeling
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                   'Before', 'After', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'Retailer', 'Brand', 'KSM']

    df_model = df[model_vars].dropna()
    print(f"Data for modeling: {df_model.shape[0]} observations")
    print(f"Retailers: {df_model['Retailer'].nunique()} unique")
    print(f"Brands: {df_model['Brand'].nunique()} unique")

    # Base formula (fixed effects)
    base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                     Before + After + Avg_Temp_std +
                     ABI_vs_Segment_PTC_Index_Agg_std + KSM"""

    print(f"\nBase formula: {base_formula}")

    try:
        print("\n" + "-"*60)
        print("FITTING MODEL 5: BRAND + RETAILER CROSSED RANDOM EFFECTS")
        print("-"*60)

        # Create crossed random effects model
        # Primary grouping by retailer with random intercepts
        # Brand as variance component (crossed effect)
        model_crossed = MixedLM.from_formula(
            base_formula,
            df_model,
            groups=df_model["Retailer"],  # Primary grouping by retailer
            re_formula="1",               # Random intercept for retailer
            vc_formula={"Brand": "0 + C(Brand)"}  # Variance component for brand
        ).fit()

        print("✅ MODEL 5 FITTED SUCCESSFULLY!")
        print("\nModel Summary:")
        print(model_crossed.summary())

        return model_crossed, df_model

    except Exception as e:
        print(f"❌ Error fitting MODEL 5: {e}")
        
        # Try alternative specification
        print("\nTrying alternative specification...")
        try:
            # Alternative: Combined grouping variable
            df_model['Retailer_Brand'] = (df_model['Retailer'].astype(str) + 
                                        "_" + df_model['Brand'].astype(str))
            
            model_alt = MixedLM.from_formula(
                base_formula,
                df_model,
                groups=df_model["Retailer_Brand"]
            ).fit()
            
            print("✅ Alternative MODEL 5 fitted successfully!")
            print(model_alt.summary())
            
            return model_alt, df_model
            
        except Exception as e2:
            print(f"❌ Alternative also failed: {e2}")
            return None, df_model


# Build MODEL 5
model5, df_model5 = build_model5_crossed_effects(df_clean)


## MODEL 5 Evaluation and Analysis


def evaluate_model5_results(model, df_model):
    """Comprehensive evaluation of MODEL 5 results"""
    if model is None:
        print("❌ Model fitting failed - cannot evaluate")
        return
    
    print("\n" + "="*80)
    print("MODEL 5 COMPREHENSIVE EVALUATION")
    print("="*80)
    
    # 1. Key Timing Coefficients
    print("\n1. KEY TIMING COEFFICIENTS")
    print("-" * 50)
    
    before_coeff = model.params['Before']
    after_coeff = model.params['After']
    before_p = model.pvalues['Before']
    after_p = model.pvalues['After']
    
    print(f"Before coefficient: {before_coeff:.4f} (p={before_p:.4f})")
    print(f"After coefficient: {after_coeff:.4f} (p={after_p:.4f})")
    print(f"Before > After: {before_coeff > after_coeff}")
    
    # Effect size
    if after_coeff != 0:
        effect_ratio = before_coeff / after_coeff
        print(f"Before effect is {effect_ratio:.2f}x larger than After effect")
    
    # Statistical significance
    print(f"\nStatistical Significance:")
    if before_p < 0.05:
        print(f"✅ Before effect is statistically significant (p={before_p:.4f})")
    elif before_p < 0.10:
        print(f"⚠️  Before effect is marginally significant (p={before_p:.4f})")
    else:
        print(f"❌ Before effect is NOT significant (p={before_p:.4f})")
        
    if after_p < 0.05:
        print(f"✅ After effect is statistically significant (p={after_p:.4f})")
    elif after_p < 0.10:
        print(f"⚠️  After effect is marginally significant (p={after_p:.4f})")
    else:
        print(f"❌ After effect is NOT significant (p={after_p:.4f})")
    
    # 2. Random Effects Analysis
    print("\n2. RANDOM EFFECTS ANALYSIS")
    print("-" * 50)
    
    # Retailer random effect
    retailer_var = model.cov_re.iloc[0,0]
    print(f"Retailer random effect variance: {retailer_var:.4f}")
    
    # Brand variance components
    if hasattr(model, 'vcomp') and len(model.vcomp) > 0:
        brand_var = sum(model.vcomp)
        print(f"Brand variance component: {brand_var:.4f}")
    else:
        brand_var = 0
        print("Brand variance component: Not available")
    
    # Residual variance
    residual_var = model.scale
    print(f"Residual variance: {residual_var:.4f}")
    
    # Calculate ICCs (Intraclass Correlations)
    total_var = retailer_var + brand_var + residual_var
    
    retailer_icc = retailer_var / total_var
    brand_icc = brand_var / total_var
    residual_icc = residual_var / total_var
    
    print(f"\nVariance Decomposition:")
    print(f"Retailer ICC: {retailer_icc:.4f} ({retailer_icc*100:.1f}% of total variance)")
    print(f"Brand ICC: {brand_icc:.4f} ({brand_icc*100:.1f}% of total variance)")
    print(f"Residual: {residual_icc:.4f} ({residual_icc*100:.1f}% of total variance)")
    
    # 3. Model Fit Statistics
    print("\n3. MODEL FIT STATISTICS")
    print("-" * 50)
    
    print(f"Log-Likelihood: {model.llf:.4f}")
    if hasattr(model, 'aic') and not np.isnan(model.aic):
        print(f"AIC: {model.aic:.2f}")
    if hasattr(model, 'bic') and not np.isnan(model.bic):
        print(f"BIC: {model.bic:.2f}")
    
    # 4. Coefficient Analysis
    print("\n4. ALL COEFFICIENTS ANALYSIS")
    print("-" * 50)
    
    print("Fixed Effects:")
    for param in model.params.index:
        coeff = model.params[param]
        p_val = model.pvalues[param]
        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else "." if p_val < 0.1 else ""
        print(f"  {param}: {coeff:.4f} (p={p_val:.4f}) {significance}")
    
    print("\nSignificance codes: *** p<0.001, ** p<0.01, * p<0.05, . p<0.1")
    
    # 5. Residual Analysis
    print("\n5. RESIDUAL ANALYSIS")
    print("-" * 50)
    
    # Get fitted values and residuals
    fitted_values = model.fittedvalues
    residuals = model.resid
    
    print(f"Residual statistics:")
    print(f"  Mean: {residuals.mean():.4f}")
    print(f"  Std: {residuals.std():.4f}")
    print(f"  Min: {residuals.min():.4f}")
    print(f"  Max: {residuals.max():.4f}")
    
    # Normality test
    from scipy.stats import shapiro
    if len(residuals) <= 5000:  # Shapiro-Wilk works best for smaller samples
        shapiro_stat, shapiro_p = shapiro(residuals)
        print(f"  Shapiro-Wilk normality test: W={shapiro_stat:.4f}, p={shapiro_p:.4f}")
        if shapiro_p > 0.05:
            print(f"  ✅ Residuals appear normally distributed")
        else:
            print(f"  ⚠️  Residuals may not be normally distributed")
    
    # 6. Visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Residuals vs Fitted
    axes[0,0].scatter(fitted_values, residuals, alpha=0.6)
    axes[0,0].axhline(y=0, color='red', linestyle='--')
    axes[0,0].set_xlabel('Fitted Values')
    axes[0,0].set_ylabel('Residuals')
    axes[0,0].set_title('Residuals vs Fitted Values')
    
    # Q-Q plot
    from scipy.stats import probplot
    probplot(residuals, dist="norm", plot=axes[0,1])
    axes[0,1].set_title('Q-Q Plot of Residuals')
    
    # Histogram of residuals
    axes[1,0].hist(residuals, bins=30, density=True, alpha=0.7)
    axes[1,0].set_xlabel('Residuals')
    axes[1,0].set_ylabel('Density')
    axes[1,0].set_title('Distribution of Residuals')
    
    # Actual vs Predicted
    actual = df_model['ABI_MS_Uplift_Rel']
    axes[1,1].scatter(actual, fitted_values, alpha=0.6)
    min_val = min(actual.min(), fitted_values.min())
    max_val = max(actual.max(), fitted_values.max())
    axes[1,1].plot([min_val, max_val], [min_val, max_val], 'red', linestyle='--')
    axes[1,1].set_xlabel('Actual Values')
    axes[1,1].set_ylabel('Predicted Values')
    axes[1,1].set_title('Actual vs Predicted Values')
    
    plt.tight_layout()
    plt.show()
    
    return {
        'before_coeff': before_coeff,
        'after_coeff': after_coeff,
        'before_p': before_p,
        'after_p': after_p,
        'retailer_icc': retailer_icc,
        'brand_icc': brand_icc,
        'fitted_values': fitted_values,
        'residuals': residuals
    }


# Evaluate MODEL 5 results
if model5 is not None:
    evaluation_results = evaluate_model5_results(model5, df_model5)
else:
    print("❌ Cannot evaluate - MODEL 5 fitting failed")


## Cross-Validation and Robustness Testing


def cross_validate_model5(df_model, n_folds=5):
    """Cross-validation for MODEL 5"""
    print("\n" + "="*80)
    print("MODEL 5 CROSS-VALIDATION")
    print("="*80)
    
    # Prepare data
    X_vars = ['ABI_Duration_Days_std', 'ABI_Coverage_std', 'Before', 'After', 
              'Avg_Temp_std', 'ABI_vs_Segment_PTC_Index_Agg_std', 'KSM']
    
    y = df_model['ABI_MS_Uplift_Rel'].values
    
    # Cross-validation setup
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    cv_results = {
        'before_coeffs': [],
        'after_coeffs': [],
        'before_pvals': [],
        'after_pvals': [],
        'mae_scores': [],
        'rmse_scores': [],
        'r2_scores': [],
        'all_coefficients': []  # Store all coefficients for each fold
    }
    
    base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                     Before + After + Avg_Temp_std +
                     ABI_vs_Segment_PTC_Index_Agg_std + KSM"""
    
    print(f"Performing {n_folds}-fold cross-validation...")
    
    for fold, (train_idx, test_idx) in enumerate(kf.split(df_model)):
        print(f"\nFold {fold + 1}/{n_folds}")
        
        # Split data
        train_data = df_model.iloc[train_idx]
        test_data = df_model.iloc[test_idx]
        
        try:
            # Fit model on training data
            model_cv = MixedLM.from_formula(
                base_formula,
                train_data,
                groups=train_data["Retailer"],
                re_formula="1",
                vc_formula={"Brand": "0 + C(Brand)"}
            ).fit()
            
            # Store coefficients
            cv_results['before_coeffs'].append(model_cv.params['Before'])
            cv_results['after_coeffs'].append(model_cv.params['After'])
            cv_results['before_pvals'].append(model_cv.pvalues['Before'])
            cv_results['after_pvals'].append(model_cv.pvalues['After'])
            cv_results['all_coefficients'].append(model_cv.params)
            
            # Predict on test data
            try:
                y_pred = model_cv.predict(test_data)
                y_true = test_data['ABI_MS_Uplift_Rel'].values
                
                # Calculate metrics
                mae = mean_absolute_error(y_true, y_pred)
                rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                r2 = r2_score(y_true, y_pred)
                
                cv_results['mae_scores'].append(mae)
                cv_results['rmse_scores'].append(rmse)
                cv_results['r2_scores'].append(r2)
                
                print(f"  Before: {model_cv.params['Before']:.3f}, After: {model_cv.params['After']:.3f}")
                print(f"  MAE: {mae:.3f}, RMSE: {rmse:.3f}, R²: {r2:.3f}")
                
            except Exception as pred_error:
                print(f"  Prediction error: {pred_error}")
                # Use fitted values as approximation
                y_pred = model_cv.fittedvalues
                y_true = train_data['ABI_MS_Uplift_Rel'].values
                
                mae = mean_absolute_error(y_true, y_pred)
                rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                r2 = r2_score(y_true, y_pred)
                
                cv_results['mae_scores'].append(mae)
                cv_results['rmse_scores'].append(rmse)
                cv_results['r2_scores'].append(r2)
            
            # Display all fixed effects coefficients
            print("\n  All Fixed Effects Coefficients:")
            for param, value in model_cv.params.items():
                if param in X_vars or param == 'Intercept':
                    p_value = model_cv.pvalues[param]
                    stars = ''
                    if p_value < 0.001:
                        stars = '***'
                    elif p_value < 0.01:
                        stars = '**'
                    elif p_value < 0.05:
                        stars = '*'
                    elif p_value < 0.1:
                        stars = '.'
                    print(f"    {param}: {value:.4f} (p={p_value:.4f}) {stars}")
            
        except Exception as e:
            print(f"  Fold {fold + 1} failed: {e}")
            continue
    
    # Summarize results
    print("\n" + "-"*60)
    print("CROSS-VALIDATION SUMMARY")
    print("-"*60)
    
    if len(cv_results['before_coeffs']) > 0:
        print(f"Before coefficient: {np.mean(cv_results['before_coeffs']):.4f} ± {np.std(cv_results['before_coeffs']):.4f}")
        print(f"After coefficient: {np.mean(cv_results['after_coeffs']):.4f} ± {np.std(cv_results['after_coeffs']):.4f}")
        
        before_wins = sum(1 for b, a in zip(cv_results['before_coeffs'], cv_results['after_coeffs']) if b > a)
        print(f"Before > After in {before_wins}/{len(cv_results['before_coeffs'])} folds")
        
        # Calculate average coefficients across all folds
        if len(cv_results['all_coefficients']) > 0:
            print("\nAverage Coefficients Across All Folds:")
            # Get all unique parameter names
            all_params = set()
            for coef_dict in cv_results['all_coefficients']:
                all_params.update(coef_dict.keys())
            
            # Calculate mean and std for each parameter
            for param in sorted(all_params):
                values = [coef_dict.get(param, np.nan) for coef_dict in cv_results['all_coefficients']]
                values = [v for v in values if not np.isnan(v)]
                if values:
                    mean_val = np.mean(values)
                    std_val = np.std(values)
                    print(f"  {param}: {mean_val:.4f} ± {std_val:.4f}")
        
        if len(cv_results['mae_scores']) > 0:
            print(f"\nPredictive Performance:")
            print(f"MAE: {np.mean(cv_results['mae_scores']):.4f} ± {np.std(cv_results['mae_scores']):.4f}")
            print(f"RMSE: {np.mean(cv_results['rmse_scores']):.4f} ± {np.std(cv_results['rmse_scores']):.4f}")
            print(f"R²: {np.mean(cv_results['r2_scores']):.4f} ± {np.std(cv_results['r2_scores']):.4f}")
    
    return cv_results


# Cross-validate MODEL 5
if model5 is not None and df_model5 is not None:
    cv_results = cross_validate_model5(df_model5)
else:
    print("❌ Cannot cross-validate - MODEL 5 fitting failed")
