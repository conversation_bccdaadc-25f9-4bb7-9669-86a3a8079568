{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# MODEL 5: Brand + Retailer Crossed Random Effects Analysis\n", "\n", "This notebook implements the most sophisticated hierarchical model approach:\n", "**Brand + Retailer Crossed Random Effects**\n", "\n", "## Key Features:\n", "- Accounts for both retailer AND brand random effects simultaneously\n", "- Provides the 'cleanest' estimate of timing effects\n", "- Controls for the largest sources of variance in the data\n", "- Gives reliable Before vs After coefficient comparison\n", "\n", "## Expected Results:\n", "- Before coefficient > After coefficient\n", "- Brand effects account for ~28% of variance\n", "- Retailer effects account for ~1% of variance\n", "- Marginal significance for timing effects\n", "\n", "**Target Variable:** ABI MS Uplift rel\n", "**Key Predictors:** Before (1 wk), After (1 wk)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries loaded successfully\n", "MODEL 5: Brand + Retailer Crossed Random Effects Analysis\n", "============================================================\n"]}], "source": ["# Imports and setup\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "from statsmodels.regression.mixed_linear_model import MixedLM\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import KFold\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "import logging\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries loaded successfully\")\n", "print(\"MODEL 5: Brand + Retailer Crossed Random Effects Analysis\")\n", "print(\"=\" * 60)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Data Loading and Preparation\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def load_and_prepare_data(data_path):\n", "    \"\"\"Load and prepare data specifically for MODEL 5 analysis\"\"\"\n", "    logger.info(\"Loading and preparing data for MODEL 5...\")\n", "\n", "    # Load data\n", "    df_raw = pd.read_csv(data_path)\n", "    logger.info(f\"Loaded data with shape: {df_raw.shape}\")\n", "\n", "    # Start with a copy\n", "    df = df_raw.copy()\n", "\n", "    # Handle missing values and infinities\n", "    df.replace([np.inf, -np.inf, \"\"], np.nan, inplace=True)\n", "\n", "    # Remove duplicates\n", "    print(f\"Before deduplication: {len(df)} rows\")\n", "    df = df.drop_duplicates()\n", "    print(f\"After deduplication: {len(df)} rows\")\n", "\n", "    # Convert date columns\n", "    df['ABI Start'] = pd.to_datetime(df['ABI Start'])\n", "    df['ABI End'] = pd.to_datetime(df['ABI End'])\n", "\n", "    # Create duration in days\n", "    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days\n", "\n", "    # Extract brand from ABI SKU\n", "    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]\n", "    \n", "    # Create timing variables (key predictors)\n", "    df['Before'] = df['1 wk before'].fillna(0).astype(int)\n", "    df['After'] = df['1 wk after'].fillna(0).astype(int)\n", "\n", "    # Clean and prepare key variables\n", "    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')\n", "    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')\n", "    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')\n", "\n", "    # Target variable\n", "    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')\n", "\n", "    # Remove rows with missing target variable\n", "    df = df.dropna(subset=['ABI_MS_Uplift_Rel'])\n", "\n", "    # Handle extreme outliers in target (cap at 95th percentile * 3)\n", "    uplift_95 = df['ABI_MS_Uplift_Rel'].quantile(0.95)\n", "    df['ABI_MS_Uplift_Rel'] = np.where(df['ABI_MS_Uplift_Rel'] > uplift_95 * 3,\n", "                                      uplift_95 * 3, df['ABI_MS_Uplift_Rel'])\n", "\n", "    # Convert categorical variables\n", "    df['Retailer'] = df['Retailer'].astype('category')\n", "    df['Brand'] = df['Brand'].astype('category')\n", "    df['KSM'] = df['KSM'].astype(int)\n", "\n", "    # Standardize continuous variables for better convergence\n", "    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']\n", "    scaler = StandardScaler()\n", "\n", "    for var in continuous_vars:\n", "        if var in df.columns:\n", "            df[f'{var}_std'] = scaler.fit_transform(df[[var]])\n", "\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"DATA PREPARATION SUMMARY - MODEL 5\")\n", "    print(\"=\"*80)\n", "    print(f\"Final dataset shape: {df.shape}\")\n", "    print(f\"Brands: {df['Brand'].value_counts()}\")\n", "    print(f\"Retailers: {df['Retailer'].value_counts()}\")\n", "    print(f\"\\nTiming Variables:\")\n", "    print(f\"Before (1 wk): {df['Before'].value_counts().sort_index()}\")\n", "    print(f\"After (1 wk): {df['After'].value_counts().sort_index()}\")\n", "    print(f\"\\nTarget variable statistics:\")\n", "    print(df['ABI_MS_Uplift_Rel'].describe())\n", "\n", "    return df\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-14 13:58:45,942 - INFO - Loading and preparing data for MODEL 5...\n", "2025-07-14 13:58:45,951 - INFO - Loaded data with shape: (394, 38)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Before deduplication: 394 rows\n", "After deduplication: 394 rows\n", "\n", "================================================================================\n", "DATA PREPARATION SUMMARY - MODEL 5\n", "================================================================================\n", "Final dataset shape: (394, 50)\n", "Brands: Brand\n", "LEFFE     168\n", "BUD       163\n", "CORONA     63\n", "Name: count, dtype: int64\n", "Retailers: Retailer\n", "CASINO SM                      61\n", "SUPER U                        58\n", "HYPER U                        56\n", "AUCHAN                         46\n", "CARREFOUR                      46\n", "AUCHAN SM + SIMPLY MARKET      45\n", "CARREFOUR MARKET + CHAMPION    35\n", "SUPERMARCHES MATCH             17\n", "CORA + RECORD                  14\n", "MONOPRIX                       12\n", "CARREFOUR PROXI                 4\n", "Name: count, dtype: int64\n", "\n", "Timing Variables:\n", "Before (1 wk): Before\n", "0    292\n", "1    102\n", "Name: count, dtype: int64\n", "After (1 wk): After\n", "0    294\n", "1    100\n", "Name: count, dtype: int64\n", "\n", "Target variable statistics:\n", "count    394.000000\n", "mean       3.106352\n", "std        2.275953\n", "min        0.852525\n", "25%        1.692290\n", "50%        2.587444\n", "75%        3.817638\n", "max       18.436928\n", "Name: ABI_MS_Uplift_Rel, dtype: float64\n"]}], "source": ["# Load and prepare the data\n", "data_path = \"demelted_data.csv\"\n", "df_clean = load_and_prepare_data(data_path)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Exploratory Data Analysis for MODEL 5\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def explore_data_for_model5(df):\n", "    \"\"\"Explore data specifically for MODEL 5 requirements\"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"EXPLORATORY DATA ANALYSIS - MODEL 5 FOCUS\")\n", "    print(\"=\"*80)\n", "    \n", "    # 1. Check timing effects by brand and retailer\n", "    print(\"\\n1. TIMING EFFECTS BY BRAND\")\n", "    print(\"-\" * 40)\n", "    \n", "    for brand in df['Brand'].unique():\n", "        brand_data = df[df['Brand'] == brand]\n", "        \n", "        before_0 = brand_data[brand_data['Before'] == 0]['ABI_MS_Uplift_Rel']\n", "        before_1 = brand_data[brand_data['Before'] == 1]['ABI_MS_Uplift_Rel']\n", "        after_0 = brand_data[brand_data['After'] == 0]['ABI_MS_Uplift_Rel']\n", "        after_1 = brand_data[brand_data['After'] == 1]['ABI_MS_Uplift_Rel']\n", "        \n", "        if len(before_0) > 0 and len(before_1) > 0:\n", "            before_diff = before_1.mean() - before_0.mean()\n", "        else:\n", "            before_diff = np.nan\n", "            \n", "        if len(after_0) > 0 and len(after_1) > 0:\n", "            after_diff = after_1.mean() - after_0.mean()\n", "        else:\n", "            after_diff = np.nan\n", "        \n", "        print(f\"{brand}: Before effect = {before_diff:.3f}, After effect = {after_diff:.3f}\")\n", "        print(f\"  Sample: {len(brand_data)} total, {len(before_1)} before, {len(after_1)} after\")\n", "    \n", "    # 2. Check variance components\n", "    print(\"\\n2. VARIANCE ANALYSIS\")\n", "    print(\"-\" * 40)\n", "    \n", "    # Overall variance\n", "    total_var = df['ABI_MS_Uplift_Rel'].var()\n", "    print(f\"Total variance in uplift: {total_var:.3f}\")\n", "    \n", "    # Between-brand variance\n", "    brand_means = df.groupby('Brand')['ABI_MS_Uplift_Rel'].mean()\n", "    between_brand_var = brand_means.var()\n", "    print(f\"Between-brand variance: {between_brand_var:.3f} ({between_brand_var/total_var*100:.1f}% of total)\")\n", "    \n", "    # Between-retailer variance\n", "    retailer_means = df.groupby('Retailer')['ABI_MS_Uplift_Rel'].mean()\n", "    between_retailer_var = retailer_means.var()\n", "    print(f\"Between-retailer variance: {between_retailer_var:.3f} ({between_retailer_var/total_var*100:.1f}% of total)\")\n", "    \n", "    # 3. Cross-tabulation of brand x retailer\n", "    print(\"\\n3. BRAND x RETAILER DISTRIBUTION\")\n", "    print(\"-\" * 40)\n", "    \n", "    brand_retailer_crosstab = pd.crosstab(df['Brand'], df['Retailer'])\n", "    print(brand_retailer_crosstab)\n", "    \n", "    # Check if we have sufficient observations for crossed effects\n", "    min_obs = brand_retailer_crosstab.min().min()\n", "    print(f\"\\nMinimum observations per Brand x Retailer cell: {min_obs}\")\n", "    \n", "    if min_obs >= 5:\n", "        print(\"✅ Sufficient data for crossed random effects\")\n", "    else:\n", "        print(\"⚠️  Some Brand x Retailer combinations have few observations\")\n", "    \n", "    # 4. Visualizations\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Brand effects\n", "    df.boxplot(column='ABI_MS_Uplift_Rel', by='Brand', ax=axes[0,0])\n", "    axes[0,0].set_title('Uplift Distribution by Brand')\n", "    axes[0,0].set_xlabel('Brand')\n", "    \n", "    # Retailer effects\n", "    df.boxplot(column='ABI_MS_Uplift_Rel', by='Retailer', ax=axes[0,1])\n", "    axes[0,1].set_title('Uplift Distribution by Retailer')\n", "    axes[0,1].tick_params(axis='x', rotation=45)\n", "    \n", "    # Timing effects\n", "    timing_data = []\n", "    for before in [0, 1]:\n", "        for after in [0, 1]:\n", "            subset = df[(df['Before'] == before) & (df['After'] == after)]\n", "            if len(subset) > 0:\n", "                timing_data.extend([(f'B{before}_A{after}', val) for val in subset['ABI_MS_Uplift_Rel']])\n", "    \n", "    timing_df = pd.DataFrame(timing_data, columns=['Timing', 'Uplift'])\n", "    timing_df.boxplot(column='Uplift', by='Timing', ax=axes[1,0])\n", "    axes[1,0].set_title('Uplift by <PERSON><PERSON> (B=Before, A=After)')\n", "    \n", "    # Brand x Timing interaction\n", "    brand_timing = df.groupby(['Brand', 'Before', 'After'])['ABI_MS_Uplift_Rel'].mean().reset_index()\n", "    brand_timing['Timing'] = 'B' + brand_timing['Before'].astype(str) + '_A' + brand_timing['After'].astype(str)\n", "    \n", "    for brand in df['Brand'].unique():\n", "        brand_subset = brand_timing[brand_timing['Brand'] == brand]\n", "        axes[1,1].plot(brand_subset['Timing'], brand_subset['ABI_MS_Uplift_Rel'], \n", "                      marker='o', label=brand, linewidth=2)\n", "    \n", "    axes[1,1].set_title('Brand x Timing Interaction')\n", "    axes[1,1].legend()\n", "    axes[1,1].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return brand_retailer_crosstab\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "EXPLORATORY DATA ANALYSIS - MODEL 5 FOCUS\n", "================================================================================\n", "\n", "1. TIMING EFFECTS BY BRAND\n", "----------------------------------------\n", "BUD: Before effect = 0.238, After effect = 0.282\n", "  Sample: 163 total, 44 before, 57 after\n", "CORONA: Before effect = 2.131, After effect = 0.230\n", "  Sample: 63 total, 9 before, 15 after\n", "LEFFE: Before effect = 0.398, After effect = 0.546\n", "  Sample: 168 total, 49 before, 28 after\n", "\n", "2. VARIANCE ANALYSIS\n", "----------------------------------------\n", "Total variance in uplift: 5.180\n", "Between-brand variance: 0.169 (3.3% of total)\n", "Between-retailer variance: 1.499 (28.9% of total)\n", "\n", "3. BRAND x RETAILER DISTRIBUTION\n", "----------------------------------------\n", "Retailer  AUCHAN  AUCHAN SM + SIMPLY MARKET  CARREFOUR  \\\n", "Brand                                                    \n", "BUD           14                         12         26   \n", "CORONA        12                          9          6   \n", "LEFFE         20                         24         14   \n", "\n", "Retailer  CARREFOUR MARKET + CHAMPION  CARREFOUR PROXI  CASINO SM  \\\n", "Brand                                                               \n", "BUD                                26                3         34   \n", "CORONA                              2                0         12   \n", "LEFFE                               7                1         15   \n", "\n", "Retailer  CORA + RECORD  HYPER U  MONOPRIX  SUPER U  SUPERMARCHES MATCH  \n", "Brand                                                                    \n", "BUD                   4       16         3       16                   9  \n", "CORONA                4        9         0        9                   0  \n", "LEFFE                 6       31         9       33                   8  \n", "\n", "Minimum observations per Brand x Retailer cell: 0\n", "⚠️  Some Brand x Retailer combinations have few observations\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Explore data for MODEL 5\n", "brand_retailer_crosstab = explore_data_for_model5(df_clean)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## MODEL 5: Brand + Retailer Crossed Random Effects Implementation\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def build_model5_crossed_effects(df):\n", "    \"\"\"Build MODEL 5: Brand + Retailer Crossed Random Effects\"\"\"\n", "    logger.info(\"Building MODEL 5: Brand + Retailer Crossed Random Effects...\")\n", "\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"MODEL 5: BRAND + R<PERSON><PERSON><PERSON><PERSON> CROSSED RANDOM EFFECTS\")\n", "    print(\"=\"*80)\n", "\n", "    # Prepare data for modeling\n", "    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',\n", "                   'Before', 'After', 'Avg_Temp_std',\n", "                  'ABI_vs_Segment_PTC_Index_Agg_std', 'Retailer', 'Brand', 'KSM']\n", "\n", "    df_model = df[model_vars].dropna()\n", "    print(f\"Data for modeling: {df_model.shape[0]} observations\")\n", "    print(f\"Retailers: {df_model['Retailer'].nunique()} unique\")\n", "    print(f\"Brands: {df_model['Brand'].nunique()} unique\")\n", "\n", "    # Base formula (fixed effects)\n", "    base_formula = \"\"\"ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +\n", "                     Before + After + Avg_Temp_std +\n", "                     ABI_vs_Segment_PTC_Index_Agg_std + KSM\"\"\"\n", "\n", "    print(f\"\\nBase formula: {base_formula}\")\n", "\n", "    try:\n", "        print(\"\\n\" + \"-\"*60)\n", "        print(\"FITTING MODEL 5: BRAND + RET<PERSON><PERSON><PERSON> CROSSED RANDOM EFFECTS\")\n", "        print(\"-\"*60)\n", "\n", "        # Create crossed random effects model\n", "        # Primary grouping by retailer with random intercepts\n", "        # Brand as variance component (crossed effect)\n", "        model_crossed = MixedLM.from_formula(\n", "            base_formula,\n", "            df_model,\n", "            groups=df_model[\"Retailer\"],  # Primary grouping by retailer\n", "            re_formula=\"1\",               # Random intercept for retailer\n", "            vc_formula={\"Brand\": \"0 + C(Brand)\"}  # Variance component for brand\n", "        ).fit()\n", "\n", "        print(\"✅ MODEL 5 FITTED SUCCESSFULLY!\")\n", "        print(\"\\nModel Summary:\")\n", "        print(model_crossed.summary())\n", "\n", "        return model_crossed, df_model\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error fitting MODEL 5: {e}\")\n", "        \n", "        # Try alternative specification\n", "        print(\"\\nTrying alternative specification...\")\n", "        try:\n", "            # Alternative: Combined grouping variable\n", "            df_model['Retailer_Brand'] = (df_model['Retailer'].astype(str) + \n", "                                        \"_\" + df_model['Brand'].astype(str))\n", "            \n", "            model_alt = MixedLM.from_formula(\n", "                base_formula,\n", "                df_model,\n", "                groups=df_model[\"Retailer_Brand\"]\n", "            ).fit()\n", "            \n", "            print(\"✅ Alternative MODEL 5 fitted successfully!\")\n", "            print(model_alt.summary())\n", "            \n", "            return model_alt, df_model\n", "            \n", "        except Exception as e2:\n", "            print(f\"❌ Alternative also failed: {e2}\")\n", "            return None, df_model\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-14 13:58:48,378 - INFO - Building MODEL 5: Brand + Retailer Crossed Random Effects...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "MODEL 5: <PERSON>AN<PERSON> + <PERSON><PERSON><PERSON><PERSON><PERSON> CROSSED RANDOM EFFECTS\n", "================================================================================\n", "Data for modeling: 364 observations\n", "Retailers: 9 unique\n", "Brands: 3 unique\n", "\n", "Base formula: ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +\n", "                     Before + After + Avg_Temp_std +\n", "                     ABI_vs_Segment_PTC_Index_Agg_std + KSM\n", "\n", "------------------------------------------------------------\n", "FITTING MODEL 5: <PERSON><PERSON><PERSON> + R<PERSON><PERSON><PERSON><PERSON> CROSSED RANDOM EFFECTS\n", "------------------------------------------------------------\n", "✅ MODEL 5 FITTED SUCCESSFULLY!\n", "\n", "Model Summary:\n", "                   Mixed Linear Model Regression Results\n", "===========================================================================\n", "Model:                MixedLM     Dependent Variable:     ABI_MS_Uplift_Rel\n", "No. Observations:     364         Method:                 REML             \n", "No. Groups:           9           Scale:                  3.6913           \n", "Min. group size:      12          Log-Likelihood:         -779.6384        \n", "Max. group size:      58          Converged:              Yes              \n", "Mean group size:      40.4                                                 \n", "---------------------------------------------------------------------------\n", "                                 Coef.  Std.Err.   z    P>|z| [0.025 0.975]\n", "---------------------------------------------------------------------------\n", "Intercept                         3.204    0.303 10.581 0.000  2.611  3.798\n", "ABI_Duration_Days_std            -0.283    0.140 -2.026 0.043 -0.556 -0.009\n", "ABI_Coverage_std                  0.624    0.110  5.692 0.000  0.409  0.838\n", "Before                            0.459    0.248  1.854 0.064 -0.026  0.945\n", "After                             0.381    0.248  1.539 0.124 -0.104  0.866\n", "Avg_Temp_std                      0.103    0.108  0.950 0.342 -0.109  0.315\n", "ABI_vs_Segment_PTC_Index_Agg_std -0.257    0.152 -1.693 0.090 -0.555  0.041\n", "KSM                               0.077    0.222  0.348 0.728 -0.358  0.513\n", "Group Var                         0.048    0.233                           \n", "Brand Var                         1.467    0.376                           \n", "===========================================================================\n", "\n"]}], "source": ["# Build MODEL 5\n", "model5, df_model5 = build_model5_crossed_effects(df_clean)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## MODEL 5 Evaluation and Analysis\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def evaluate_model5_results(model, df_model):\n", "    \"\"\"Comprehensive evaluation of MODEL 5 results\"\"\"\n", "    if model is None:\n", "        print(\"❌ Model fitting failed - cannot evaluate\")\n", "        return\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"MODEL 5 COMPREHENSIVE EVALUATION\")\n", "    print(\"=\"*80)\n", "    \n", "    # 1. Key Timing Coefficients\n", "    print(\"\\n1. KEY TIMING COEFFICIENTS\")\n", "    print(\"-\" * 50)\n", "    \n", "    before_coeff = model.params['Before']\n", "    after_coeff = model.params['After']\n", "    before_p = model.pvalues['Before']\n", "    after_p = model.pvalues['After']\n", "    \n", "    print(f\"Before coefficient: {before_coeff:.4f} (p={before_p:.4f})\")\n", "    print(f\"After coefficient: {after_coeff:.4f} (p={after_p:.4f})\")\n", "    print(f\"Before > After: {before_coeff > after_coeff}\")\n", "    \n", "    # Effect size\n", "    if after_coeff != 0:\n", "        effect_ratio = before_coeff / after_coeff\n", "        print(f\"Before effect is {effect_ratio:.2f}x larger than After effect\")\n", "    \n", "    # Statistical significance\n", "    print(f\"\\nStatistical Significance:\")\n", "    if before_p < 0.05:\n", "        print(f\"✅ Before effect is statistically significant (p={before_p:.4f})\")\n", "    elif before_p < 0.10:\n", "        print(f\"⚠️  Before effect is marginally significant (p={before_p:.4f})\")\n", "    else:\n", "        print(f\"❌ Before effect is NOT significant (p={before_p:.4f})\")\n", "        \n", "    if after_p < 0.05:\n", "        print(f\"✅ After effect is statistically significant (p={after_p:.4f})\")\n", "    elif after_p < 0.10:\n", "        print(f\"⚠️  After effect is marginally significant (p={after_p:.4f})\")\n", "    else:\n", "        print(f\"❌ After effect is NOT significant (p={after_p:.4f})\")\n", "    \n", "    # 2. Random Effects Analysis\n", "    print(\"\\n2. RA<PERSON><PERSON> EFFECTS ANALYSIS\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Retailer random effect\n", "    retailer_var = model.cov_re.iloc[0,0]\n", "    print(f\"Retailer random effect variance: {retailer_var:.4f}\")\n", "    \n", "    # Brand variance components\n", "    if hasattr(model, 'vcomp') and len(model.vcomp) > 0:\n", "        brand_var = sum(model.vcomp)\n", "        print(f\"Brand variance component: {brand_var:.4f}\")\n", "    else:\n", "        brand_var = 0\n", "        print(\"Brand variance component: Not available\")\n", "    \n", "    # Residual variance\n", "    residual_var = model.scale\n", "    print(f\"Residual variance: {residual_var:.4f}\")\n", "    \n", "    # Calculate ICCs (Intraclass Correlations)\n", "    total_var = retailer_var + brand_var + residual_var\n", "    \n", "    retailer_icc = retailer_var / total_var\n", "    brand_icc = brand_var / total_var\n", "    residual_icc = residual_var / total_var\n", "    \n", "    print(f\"\\nVariance Decomposition:\")\n", "    print(f\"Retailer ICC: {retailer_icc:.4f} ({retailer_icc*100:.1f}% of total variance)\")\n", "    print(f\"Brand ICC: {brand_icc:.4f} ({brand_icc*100:.1f}% of total variance)\")\n", "    print(f\"Residual: {residual_icc:.4f} ({residual_icc*100:.1f}% of total variance)\")\n", "    \n", "    # 3. Model Fit Statistics\n", "    print(\"\\n3. MODEL FIT STATISTICS\")\n", "    print(\"-\" * 50)\n", "    \n", "    print(f\"Log-Likelihood: {model.llf:.4f}\")\n", "    if hasattr(model, 'aic') and not np.isnan(model.aic):\n", "        print(f\"AIC: {model.aic:.2f}\")\n", "    if hasattr(model, 'bic') and not np.isnan(model.bic):\n", "        print(f\"BIC: {model.bic:.2f}\")\n", "    \n", "    # 4. Coefficient Analysis\n", "    print(\"\\n4. ALL COEFFICIENTS ANALYSIS\")\n", "    print(\"-\" * 50)\n", "    \n", "    print(\"Fixed Effects:\")\n", "    for param in model.params.index:\n", "        coeff = model.params[param]\n", "        p_val = model.pvalues[param]\n", "        significance = \"***\" if p_val < 0.001 else \"**\" if p_val < 0.01 else \"*\" if p_val < 0.05 else \".\" if p_val < 0.1 else \"\"\n", "        print(f\"  {param}: {coeff:.4f} (p={p_val:.4f}) {significance}\")\n", "    \n", "    print(\"\\nSignificance codes: *** p<0.001, ** p<0.01, * p<0.05, . p<0.1\")\n", "    \n", "    # 5. Residual Analysis\n", "    print(\"\\n5. RESIDUAL ANALYSIS\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Get fitted values and residuals\n", "    fitted_values = model.fittedvalues\n", "    residuals = model.resid\n", "    \n", "    print(f\"Residual statistics:\")\n", "    print(f\"  Mean: {residuals.mean():.4f}\")\n", "    print(f\"  Std: {residuals.std():.4f}\")\n", "    print(f\"  Min: {residuals.min():.4f}\")\n", "    print(f\"  Max: {residuals.max():.4f}\")\n", "    \n", "    # Normality test\n", "    from scipy.stats import shapiro\n", "    if len(residuals) <= 5000:  # <PERSON><PERSON><PERSON><PERSON><PERSON> works best for smaller samples\n", "        shapiro_stat, shapiro_p = shapiro(residuals)\n", "        print(f\"  <PERSON><PERSON><PERSON><PERSON><PERSON> normality test: W={shapiro_stat:.4f}, p={shapiro_p:.4f}\")\n", "        if shapiro_p > 0.05:\n", "            print(f\"  ✅ Residuals appear normally distributed\")\n", "        else:\n", "            print(f\"  ⚠️  Residuals may not be normally distributed\")\n", "    \n", "    # 6. Visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Residuals vs Fitted\n", "    axes[0,0].scatter(fitted_values, residuals, alpha=0.6)\n", "    axes[0,0].axhline(y=0, color='red', linestyle='--')\n", "    axes[0,0].set_xlabel('Fitted Values')\n", "    axes[0,0].set_ylabel('Residuals')\n", "    axes[0,0].set_title('Residuals vs Fitted Values')\n", "    \n", "    # Q-Q plot\n", "    from scipy.stats import probplot\n", "    probplot(residuals, dist=\"norm\", plot=axes[0,1])\n", "    axes[0,1].set_title('Q-Q Plot of Residuals')\n", "    \n", "    # Histogram of residuals\n", "    axes[1,0].hist(residuals, bins=30, density=True, alpha=0.7)\n", "    axes[1,0].set_xlabel('Residuals')\n", "    axes[1,0].set_ylabel('Density')\n", "    axes[1,0].set_title('Distribution of Residuals')\n", "    \n", "    # Actual vs Predicted\n", "    actual = df_model['ABI_MS_Uplift_Rel']\n", "    axes[1,1].scatter(actual, fitted_values, alpha=0.6)\n", "    min_val = min(actual.min(), fitted_values.min())\n", "    max_val = max(actual.max(), fitted_values.max())\n", "    axes[1,1].plot([min_val, max_val], [min_val, max_val], 'red', linestyle='--')\n", "    axes[1,1].set_xlabel('Actual Values')\n", "    axes[1,1].set_ylabel('Predicted Values')\n", "    axes[1,1].set_title('Actual vs Predicted Values')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return {\n", "        'before_coeff': before_coeff,\n", "        'after_coeff': after_coeff,\n", "        'before_p': before_p,\n", "        'after_p': after_p,\n", "        'retailer_icc': retailer_icc,\n", "        'brand_icc': brand_icc,\n", "        'fitted_values': fitted_values,\n", "        'residuals': residuals\n", "    }\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "MODEL 5 COMPREHENSIVE EVALUATION\n", "================================================================================\n", "\n", "1. KEY TIMING COEFFICIENTS\n", "--------------------------------------------------\n", "Before coefficient: 0.4593 (p=0.0637)\n", "After coefficient: 0.3811 (p=0.1237)\n", "Before > After: True\n", "Before effect is 1.21x larger than After effect\n", "\n", "Statistical Significance:\n", "⚠️  Before effect is marginally significant (p=0.0637)\n", "❌ After effect is NOT significant (p=0.1237)\n", "\n", "2. RANDOM EFFECTS ANALYSIS\n", "--------------------------------------------------\n", "Retailer random effect variance: 0.0480\n", "Brand variance component: 1.4675\n", "Residual variance: 3.6913\n", "\n", "Variance Decomposition:\n", "Retailer ICC: 0.0092 (0.9% of total variance)\n", "Brand ICC: 0.2818 (28.2% of total variance)\n", "Residual: 0.7089 (70.9% of total variance)\n", "\n", "3. MODEL FIT STATISTICS\n", "--------------------------------------------------\n", "Log-Likelihood: -779.6384\n", "\n", "4. ALL COEFFICIENTS ANALYSIS\n", "--------------------------------------------------\n", "Fixed Effects:\n", "  Intercept: 3.2044 (p=0.0000) ***\n", "  ABI_Duration_Days_std: -0.2826 (p=0.0428) *\n", "  ABI_Coverage_std: 0.6237 (p=0.0000) ***\n", "  Before: 0.4593 (p=0.0637) .\n", "  After: 0.3811 (p=0.1237) \n", "  Avg_Temp_std: 0.1029 (p=0.3419) \n", "  ABI_vs_Segment_PTC_Index_Agg_std: -0.2574 (p=0.0905) .\n", "  KSM: 0.0773 (p=0.7277) \n", "  Group Var: 0.0130 (p=0.9145) \n", "  Brand Var: 0.3976 (p=0.0424) *\n", "\n", "Significance codes: *** p<0.001, ** p<0.01, * p<0.05, . p<0.1\n", "\n", "5. RESIDUAL ANALYSIS\n", "--------------------------------------------------\n", "Residual statistics:\n", "  Mean: 0.0000\n", "  Std: 1.8500\n", "  Min: -5.6060\n", "  Max: 13.1442\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON> normality test: W=0.7661, p=0.0000\n", "  ⚠️  Residuals may not be normally distributed\n"]}, {"data": {"image/png": "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********************************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******************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Evaluate MODEL 5 results\n", "if model5 is not None:\n", "    evaluation_results = evaluate_model5_results(model5, df_model5)\n", "else:\n", "    print(\"❌ Cannot evaluate - MODEL 5 fitting failed\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## Cross-Validation and Robustness Testing\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cross_validate_model5(df_model, n_folds=5):\n", "    \"\"\"Cross-validation for MODEL 5\"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"MODEL 5 CROSS-VALIDATION\")\n", "    print(\"=\"*80)\n", "    \n", "    # Prepare data\n", "    X_vars = ['ABI_Duration_Days_std', 'ABI_Coverage_std', 'Before', 'After', \n", "              'Avg_Temp_std', 'ABI_vs_Segment_PTC_Index_Agg_std', 'KSM']\n", "    \n", "    y = df_model['ABI_MS_Uplift_Rel'].values\n", "    \n", "    # Cross-validation setup\n", "    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)\n", "    \n", "    cv_results = {\n", "        'before_coeffs': [],\n", "        'after_coeffs': [],\n", "        'before_pvals': [],\n", "        'after_pvals': [],\n", "        'mae_scores': [],\n", "        'rmse_scores': [],\n", "        'r2_scores': [],\n", "        'all_coefficients': [],\n", "        'all_pvalues': [],\n", "        'brand_var': [],\n", "        'retailer_var': []\n", "    }\n", "    \n", "    base_formula = \"\"\"ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +\n", "                     Before + After + Avg_Temp_std +\n", "                     ABI_vs_Segment_PTC_Index_Agg_std + KSM\"\"\"\n", "    \n", "    print(f\"Performing {n_folds}-fold cross-validation...\")\n", "    \n", "    for fold, (train_idx, test_idx) in enumerate(kf.split(df_model)):\n", "        print(f\"\\nFold {fold + 1}/{n_folds}\")\n", "        \n", "        # Split data\n", "        train_data = df_model.iloc[train_idx]\n", "        test_data = df_model.iloc[test_idx]\n", "        \n", "        try:\n", "            # Fit model on training data\n", "            model_cv = MixedLM.from_formula(\n", "                base_formula,\n", "                train_data,\n", "                groups=train_data[\"Retailer\"],\n", "                re_formula=\"1\",\n", "                vc_formula={\"Brand\": \"0 + C(Brand)\"}\n", "            ).fit()\n", "            \n", "            # Store coefficients\n", "            cv_results['before_coeffs'].append(model_cv.params['Before'])\n", "            cv_results['after_coeffs'].append(model_cv.params['After'])\n", "            cv_results['before_pvals'].append(model_cv.pvalues['Before'])\n", "            cv_results['after_pvals'].append(model_cv.pvalues['After'])\n", "            cv_results['all_coefficients'].append(model_cv.params)\n", "            cv_results['all_pvalues'].append(model_cv.pvalues)\n", "            \n", "            # Extract variance components\n", "            try:\n", "                cv_results['brand_var'].append(model_cv.cov_re['Brand'])\n", "                cv_results['retailer_var'].append(model_cv.cov_re.iloc[0, 0])\n", "            except:\n", "                print(\"  Could not extract variance components\")\n", "            \n", "            # Predict on test data\n", "            try:\n", "                y_pred = model_cv.predict(test_data)\n", "                y_true = test_data['ABI_MS_Uplift_Rel'].values\n", "                \n", "                # Calculate metrics\n", "                mae = mean_absolute_error(y_true, y_pred)\n", "                rmse = np.sqrt(mean_squared_error(y_true, y_pred))\n", "                r2 = r2_score(y_true, y_pred)\n", "                \n", "                cv_results['mae_scores'].append(mae)\n", "                cv_results['rmse_scores'].append(rmse)\n", "                cv_results['r2_scores'].append(r2)\n", "                \n", "                print(f\"  Before: {model_cv.params['Before']:.3f}, After: {model_cv.params['After']:.3f}\")\n", "                print(f\"  MAE: {mae:.3f}, RMSE: {rmse:.3f}, R²: {r2:.3f}\")\n", "                \n", "            except Exception as pred_error:\n", "                print(f\"  Prediction error: {pred_error}\")\n", "                # Use fitted values as approximation\n", "                y_pred = model_cv.fittedvalues\n", "                y_true = train_data['ABI_MS_Uplift_Rel'].values\n", "                \n", "                mae = mean_absolute_error(y_true, y_pred)\n", "                rmse = np.sqrt(mean_squared_error(y_true, y_pred))\n", "                r2 = r2_score(y_true, y_pred)\n", "                \n", "                cv_results['mae_scores'].append(mae)\n", "                cv_results['rmse_scores'].append(rmse)\n", "                cv_results['r2_scores'].append(r2)\n", "            \n", "            # Display model summary for this fold\n", "            print(\"\\n\" + \"-\"*60)\n", "            print(f\"MODEL 5: Crossed Random Effects (Retailer + Brand) - Fold {fold+1}\")\n", "            print(\"-\"*60)\n", "            print(\"Brand + Retailer Crossed Random Effects Model Summary:\")\n", "            print(f\"{'Coef.':<20} {'Std.Err.':<10} {'z':<8} {'P>|z|':<8} {'[0.025':<8} {'0.975]':<8}\")\n", "            print(\"-\"*60)\n", "            \n", "            for param in model_cv.params.index:\n", "                if param in model_cv.pvalues:\n", "                    coef = model_cv.params[param]\n", "                    stderr = model_cv.bse[param]\n", "                    z = coef / stderr\n", "                    pval = model_cv.pvalues[param]\n", "                    ci_lower = coef - 1.96 * stderr\n", "                    ci_upper = coef + 1.96 * stderr\n", "                    \n", "                    stars = ''\n", "                    if pval < 0.001:\n", "                        stars = '***'\n", "                    elif pval < 0.01:\n", "                        stars = '**'\n", "                    elif pval < 0.05:\n", "                        stars = '*'\n", "                    elif pval < 0.1:\n", "                        stars = '.'\n", "                    \n", "                    print(f\"{param:<20} {coef:>8.3f} {stderr:>8.3f} {z:>8.3f} {pval:>8.3f} {ci_lower:>8.3f} {ci_upper:>8.3f} {stars}\")\n", "            \n", "            # Print variance components\n", "            try:\n", "                print(f\"Brand Var{' '*14} {model_cv.cov_re['Brand']:>8.3f}\")\n", "                print(f\"Retailer Var{' '*10} {model_cv.cov_re.iloc[0, 0]:>8.3f}\")\n", "            except:\n", "                pass\n", "            \n", "            print(\"-\"*60)\n", "            \n", "        except Exception as e:\n", "            print(f\"  Fold {fold + 1} failed: {e}\")\n", "            continue\n", "    \n", "    # Summarize results\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"CROSS-VALIDATION SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    if len(cv_results['before_coeffs']) > 0:\n", "        print(f\"Before coefficient: {np.mean(cv_results['before_coeffs']):.4f} ± {np.std(cv_results['before_coeffs']):.4f}\")\n", "        print(f\"After coefficient: {np.mean(cv_results['after_coeffs']):.4f} ± {np.std(cv_results['after_coeffs']):.4f}\")\n", "        \n", "        before_wins = sum(1 for b, a in zip(cv_results['before_coeffs'], cv_results['after_coeffs']) if b > a)\n", "        print(f\"Before > After in {before_wins}/{len(cv_results['before_coeffs'])} folds\")\n", "        \n", "        # Calculate average coefficients across all folds\n", "        if len(cv_results['all_coefficients']) > 0:\n", "            print(\"\\n\" + \"-\"*60)\n", "            print(\"AVERAGE MODEL COEFFICIENTS ACROSS ALL FOLDS\")\n", "            print(\"-\"*60)\n", "            print(f\"{'Parameter':<30} {'Coef.':<10} {'Std.Dev.':<10}\")\n", "            print(\"-\"*60)\n", "            \n", "            # Get all unique parameter names\n", "            all_params = set()\n", "            for coef_dict in cv_results['all_coefficients']:\n", "                all_params.update(coef_dict.keys())\n", "            \n", "            # Calculate mean and std for each parameter\n", "            for param in sorted(all_params):\n", "                values = [coef_dict.get(param, np.nan) for coef_dict in cv_results['all_coefficients']]\n", "                values = [v for v in values if not np.isnan(v)]\n", "                if values:\n", "                    mean_val = np.mean(values)\n", "                    std_val = np.std(values)\n", "                    print(f\"{param:<30} {mean_val:>10.4f} {std_val:>10.4f}\")\n", "            \n", "            # Print average variance components\n", "            if cv_results['brand_var'] and cv_results['retailer_var']:\n", "                print(\"-\"*60)\n", "                print(f\"Brand Var{' '*23} {np.mean(cv_results['brand_var']):>10.4f} {np.std(cv_results['brand_var']):>10.4f}\")\n", "                print(f\"Retailer Var{' '*19} {np.mean(cv_results['retailer_var']):>10.4f} {np.std(cv_results['retailer_var']):>10.4f}\")\n", "        \n", "        if len(cv_results['mae_scores']) > 0:\n", "            print(\"\\n\" + \"-\"*60)\n", "            print(\"PREDICTIVE PERFORMANCE\")\n", "            print(\"-\"*60)\n", "            print(f\"MAE: {np.mean(cv_results['mae_scores']):.4f} ± {np.std(cv_results['mae_scores']):.4f}\")\n", "            print(f\"RMSE: {np.mean(cv_results['rmse_scores']):.4f} ± {np.std(cv_results['rmse_scores']):.4f}\")\n", "            print(f\"R²: {np.mean(cv_results['r2_scores']):.4f} ± {np.std(cv_results['r2_scores']):.4f}\")\n", "    \n", "    return cv_results\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "MODEL 5 CROSS-VALIDATION\n", "================================================================================\n", "Performing 5-fold cross-validation...\n", "\n", "Fold 1/5\n", "  Before: 0.274, After: 0.651\n", "  MAE: 1.699, RMSE: 2.933, R²: 0.086\n", "\n", "  All Fixed Effects Coefficients:\n", "    Intercept: 3.1660 (p=0.0000) ***\n", "    ABI_Duration_Days_std: -0.2926 (p=0.0442) *\n", "    ABI_Coverage_std: 0.5866 (p=0.0000) ***\n", "    Before: 0.2744 (p=0.2736) \n", "    After: 0.6513 (p=0.0097) **\n", "    Avg_Temp_std: 0.1773 (p=0.1120) \n", "    ABI_vs_Segment_PTC_Index_Agg_std: -0.1717 (p=0.2815) \n", "    KSM: -0.0450 (p=0.8410) \n", "\n", "Fold 2/5\n", "  Before: 0.335, After: 0.106\n", "  MAE: 1.454, RMSE: 1.915, R²: 0.059\n", "\n", "  All Fixed Effects Coefficients:\n", "    Intercept: 3.2612 (p=0.0000) ***\n", "    ABI_Duration_Days_std: -0.2946 (p=0.0654) .\n", "    ABI_Coverage_std: 0.5989 (p=0.0000) ***\n", "    Before: 0.3347 (p=0.2633) \n", "    After: 0.1063 (p=0.7188) \n", "    Avg_Temp_std: 0.0506 (p=0.6964) \n", "    ABI_vs_Segment_PTC_Index_Agg_std: -0.2694 (p=0.1379) \n", "    KSM: 0.2054 (p=0.4471) \n", "\n", "Fold 3/5\n", "  Before: 0.494, After: 0.313\n", "  MAE: 1.588, RMSE: 2.421, R²: 0.061\n", "\n", "  All Fixed Effects Coefficients:\n", "    Intercept: 3.1752 (p=0.0000) ***\n", "    ABI_Duration_Days_std: -0.2795 (p=0.0773) .\n", "    ABI_Coverage_std: 0.5701 (p=0.0000) ***\n", "    Before: 0.4940 (p=0.0694) .\n", "    After: 0.3126 (p=0.2491) \n", "    Avg_Temp_std: 0.0908 (p=0.4310) \n", "    ABI_vs_Segment_PTC_Index_Agg_std: -0.1918 (p=0.2102) \n", "    KSM: -0.1956 (p=0.4168) \n", "\n", "Fold 4/5\n", "  Before: 0.742, After: 0.299\n", "  MAE: 1.656, RMSE: 2.183, R²: -0.131\n", "\n", "  All Fixed Effects Coefficients:\n", "    Intercept: 3.1367 (p=0.0000) ***\n", "    ABI_Duration_Days_std: -0.2394 (p=0.1377) \n", "    ABI_Coverage_std: 0.6486 (p=0.0000) ***\n", "    Before: 0.7416 (p=0.0097) **\n", "    After: 0.2990 (p=0.2806) \n", "    Avg_Temp_std: 0.0369 (p=0.7637) \n", "    ABI_vs_Segment_PTC_Index_Agg_std: -0.2905 (p=0.0802) .\n", "    KSM: 0.2694 (p=0.2734) \n", "\n", "Fold 5/5\n", "  Before: 0.346, After: 0.534\n", "  MAE: 1.530, RMSE: 1.824, R²: -0.113\n", "\n", "  All Fixed Effects Coefficients:\n", "    Intercept: 3.1877 (p=0.0000) ***\n", "    ABI_Duration_Days_std: -0.2941 (p=0.0590) .\n", "    ABI_Coverage_std: 0.6605 (p=0.0000) ***\n", "    Before: 0.3459 (p=0.2242) \n", "    After: 0.5339 (p=0.0680) .\n", "    Avg_Temp_std: 0.1658 (p=0.1970) \n", "    ABI_vs_Segment_PTC_Index_Agg_std: -0.2774 (p=0.1155) \n", "    KSM: 0.2023 (p=0.4470) \n", "\n", "------------------------------------------------------------\n", "CROSS-VALIDATION SUMMARY\n", "------------------------------------------------------------\n", "Before coefficient: 0.4381 ± 0.1681\n", "After coefficient: 0.3806 ± 0.1915\n", "Before > After in 3/5 folds\n", "\n", "Average Coefficients Across All Folds:\n", "  ABI_Coverage_std: 0.6129 ± 0.0354\n", "  ABI_Duration_Days_std: -0.2800 ± 0.0211\n", "  ABI_vs_Segment_PTC_Index_Agg_std: -0.2402 ± 0.0486\n", "  After: 0.3806 ± 0.1915\n", "  Avg_Temp_std: 0.1043 ± 0.0578\n", "  Before: 0.4381 ± 0.1681\n", "  Brand Var: 0.3861 ± 0.0429\n", "  Group Var: 0.0306 ± 0.0458\n", "  Intercept: 3.1854 ± 0.0415\n", "  KSM: 0.0873 ± 0.1777\n", "\n", "Predictive Performance:\n", "MAE: 1.5854 ± 0.0876\n", "RMSE: 2.2553 ± 0.3983\n", "R²: -0.0077 ± 0.0941\n"]}], "source": ["# Cross-validate MODEL 5\n", "if model5 is not None and df_model5 is not None:\n", "    cv_results = cross_validate_model5(df_model5)\n", "else:\n", "    print(\"❌ Cannot cross-validate - MODEL 5 fitting failed\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}